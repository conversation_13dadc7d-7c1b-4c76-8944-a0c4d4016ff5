import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import matplotlib.pyplot as plt
import seaborn as sns
from data_processor import LaptopDataProcessor
from data_visualization import LaptopDataVisualizer
from model_evaluation import ModelEvaluator
from recommendation_engine import LaptopRecommendationEngine
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="🔥 Smart Laptop Recommender",
    page_icon="💻",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for glassmorphism design
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {
        font-family: 'Poppins', sans-serif;
    }
    
    /* Glassmorphism Card */
    .glass-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 20px;
        margin: 10px 0;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    /* Header Styles */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    
    .main-header h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .main-header p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }
    
    /* Recommendation Card */
    .recommendation-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        border: 1px solid rgba(255,255,255,0.2);
        padding: 20px;
        margin: 15px 0;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .recommendation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    
    /* Metric Cards */
    .metric-card {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    /* Button Styles */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    
    /* Sidebar Styles */
    .css-1d391kg {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }
    
    /* Progress Bar */
    .progress-bar {
        background: linear-gradient(90deg, #667eea, #764ba2);
        height: 10px;
        border-radius: 5px;
        margin: 10px 0;
    }
    
    /* Score Badge */
    .score-badge {
        background: linear-gradient(135deg, #11998e, #38ef7d);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin: 5px;
    }
    
    /* Price Tag */
    .price-tag {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 18px;
        display: inline-block;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'processor' not in st.session_state:
    st.session_state.processor = None
if 'engine' not in st.session_state:
    st.session_state.engine = None
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False

@st.cache_data
def load_and_process_data():
    """Load and process data with caching"""
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()
    return processor

@st.cache_resource
def initialize_engine(_processor):
    """Initialize recommendation engine with caching"""
    engine = LaptopRecommendationEngine(_processor)
    engine.train_recommendation_model()
    return engine

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🔥 Smart Laptop Recommender</h1>
        <p>Find your perfect laptop with AI-powered recommendations</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load data
    if not st.session_state.data_loaded:
        with st.spinner("🚀 Loading and processing laptop data..."):
            st.session_state.processor = load_and_process_data()
            st.session_state.engine = initialize_engine(st.session_state.processor)
            st.session_state.data_loaded = True
        st.success("✅ Data loaded successfully!")
    
    # Sidebar for navigation
    st.sidebar.title("🎯 Navigation")
    page = st.sidebar.selectbox(
        "Choose a section:",
        ["🏠 Laptop Recommender", "📊 Data Analysis", "🤖 Model Evaluation", "📈 Visualizations"]
    )
    
    if page == "🏠 Laptop Recommender":
        show_recommender_page()
    elif page == "📊 Data Analysis":
        show_data_analysis_page()
    elif page == "🤖 Model Evaluation":
        show_model_evaluation_page()
    elif page == "📈 Visualizations":
        show_visualizations_page()

def show_recommender_page():
    st.markdown("## 🎯 Find Your Perfect Laptop")
    
    # Create two columns for input
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="glass-card">', unsafe_allow_html=True)
        st.markdown("### 🎮 Usage Type")
        usage_type = st.selectbox(
            "What will you primarily use the laptop for?",
            ["Any", "Gaming", "Graphics", "Studying"],
            help="Gaming: High-performance laptops with dedicated GPUs\nGraphics: Professional workstations\nStudying: General purpose laptops"
        )
        
        st.markdown("### 💰 Budget Range")
        budget_min, budget_max = st.slider(
            "Select your budget range (€)",
            min_value=200,
            max_value=3000,
            value=(500, 1500),
            step=50
        )
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="glass-card">', unsafe_allow_html=True)
        st.markdown("### ⚖️ Weight Preference")
        weight_preference = st.selectbox(
            "Preferred laptop weight category:",
            ["Any", "Light", "Medium", "Heavy"],
            help="Light: ≤13.3\" (Ultrabooks)\nMedium: 14-15.6\" (Standard)\nHeavy: ≥17\" (Gaming/Workstation)"
        )
        
        st.markdown("### 🔢 Number of Recommendations")
        top_n = st.slider("How many recommendations?", 3, 10, 5)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Recommendation button
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 Get Recommendations", use_container_width=True):
            get_recommendations(usage_type, budget_min, budget_max, weight_preference, top_n)

def get_recommendations(usage_type, budget_min, budget_max, weight_preference, top_n):
    """Get and display recommendations"""
    
    user_preferences = {
        'usage_type': usage_type,
        'budget_min': budget_min,
        'budget_max': budget_max,
        'weight_preference': weight_preference
    }
    
    with st.spinner("🔍 Finding the best laptops for you..."):
        recommendations, message = st.session_state.engine.get_smart_recommendations(
            user_preferences, top_n
        )
    
    if recommendations.empty:
        st.error("😔 " + message)
        
        # Show alternative suggestions
        suggestions = st.session_state.engine.get_alternative_suggestions(user_preferences)
        if suggestions:
            st.markdown("### 💡 Suggestions:")
            for suggestion in suggestions:
                st.info(suggestion)
    else:
        st.success("🎉 " + message)
        
        # Display recommendations
        st.markdown("## 🏆 Your Personalized Recommendations")
        
        for i, (_, laptop) in enumerate(recommendations.iterrows(), 1):
            display_laptop_card(laptop, i)

def display_laptop_card(laptop, rank):
    """Display a laptop recommendation card"""
    
    # Create the card
    st.markdown(f"""
    <div class="recommendation-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #333;">#{rank} {laptop['Brand']} {laptop['Model']}</h3>
            <span class="score-badge">Score: {laptop['Recommendation_Score']:.1f}/100</span>
        </div>
    """, unsafe_allow_html=True)
    
    # Create columns for laptop details
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown(f"""
        **💻 Specifications:**
        - **CPU:** {laptop['CPU']}
        - **RAM:** {laptop['RAM']} GB
        - **Storage:** {laptop['Storage']} GB {laptop.get('Storage type', 'SSD')}
        - **GPU:** {laptop['GPU'] if pd.notna(laptop['GPU']) else 'Integrated'}
        - **Screen:** {laptop['Screen']}″
        """)
    
    with col2:
        st.markdown(f"""
        **🎯 Category Info:**
        - **Usage:** {laptop['Usage_Category']}
        - **Weight:** {laptop['Weight_Category']}
        - **Performance:** {laptop['Performance_Score']:.2f}/4.0
        - **Value Score:** {laptop['Value_Score']:.3f}
        """)
    
    with col3:
        st.markdown(f'<div class="price-tag">€{laptop["Final Price"]:.2f}</div>', unsafe_allow_html=True)
        st.markdown(f"**💡 Why recommended:**\n{laptop['Recommendation_Reason']}")
        
        # Purchase button
        purchase_link = laptop.get('Purchase_Link', '#')
        st.markdown(f"""
        <a href="{purchase_link}" target="_blank" style="text-decoration: none;">
            <button style="background: linear-gradient(135deg, #11998e, #38ef7d); color: white; border: none; 
                          border-radius: 25px; padding: 10px 20px; font-weight: 600; cursor: pointer; 
                          margin-top: 10px; transition: all 0.3s ease;">
                🛒 View Product
            </button>
        </a>
        """, unsafe_allow_html=True)
    
    st.markdown("</div>", unsafe_allow_html=True)
    st.markdown("---")

def show_data_analysis_page():
    """Show data analysis and statistics"""
    st.markdown("## 📊 Dataset Analysis")
    
    processor = st.session_state.processor
    df = processor.processed_df
    
    # Summary statistics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(f"""
        <div class="metric-card">
            <h3>{len(df)}</h3>
            <p>Total Laptops</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="metric-card">
            <h3>{df['Brand'].nunique()}</h3>
            <p>Brands</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="metric-card">
            <h3>€{df['Final Price'].mean():.0f}</h3>
            <p>Avg Price</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div class="metric-card">
            <h3>€{df['Final Price'].min():.0f} - €{df['Final Price'].max():.0f}</h3>
            <p>Price Range</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Data distribution charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🎯 Usage Category Distribution")
        usage_counts = df['Usage_Category'].value_counts()
        fig = px.pie(values=usage_counts.values, names=usage_counts.index, 
                    color_discrete_sequence=['#ff6b6b', '#4ecdc4', '#45b7d1'])
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("### ⚖️ Weight Category Distribution")
        weight_counts = df['Weight_Category'].value_counts()
        fig = px.bar(x=weight_counts.index, y=weight_counts.values,
                    color=weight_counts.values, color_continuous_scale='viridis')
        fig.update_layout(height=400, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    # Price analysis
    st.markdown("### 💰 Price Analysis by Brand")
    top_brands = df['Brand'].value_counts().head(10).index
    brand_data = df[df['Brand'].isin(top_brands)]
    
    fig = px.box(brand_data, x='Brand', y='Final Price', color='Brand')
    fig.update_layout(height=500, showlegend=False)
    fig.update_xaxes(tickangle=45)
    st.plotly_chart(fig, use_container_width=True)

def show_model_evaluation_page():
    """Show model evaluation results"""
    st.markdown("## 🤖 Machine Learning Model Evaluation")
    
    processor = st.session_state.processor
    
    with st.spinner("🔄 Running model evaluation..."):
        evaluator = ModelEvaluator(processor)
        results = evaluator.run_complete_evaluation()
    
    # Display comparison results
    st.markdown("### 📈 Model Performance Comparison")
    
    comparison_df = results['comparison_df']
    
    # Create metrics visualization
    fig = go.Figure()
    
    models = comparison_df['Model']
    fig.add_trace(go.Bar(
        name='Train-Test Split',
        x=models,
        y=comparison_df['Train-Test Split Accuracy'],
        marker_color='#667eea'
    ))
    
    fig.add_trace(go.Bar(
        name='K-Fold CV',
        x=models,
        y=comparison_df['K-Fold Mean Accuracy'],
        marker_color='#764ba2'
    ))
    
    fig.update_layout(
        title='Model Accuracy Comparison: Train-Test Split vs K-Fold Cross-Validation',
        xaxis_title='Models',
        yaxis_title='Accuracy',
        barmode='group',
        height=500
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Display detailed results table
    st.markdown("### 📋 Detailed Results")
    st.dataframe(comparison_df.round(4), use_container_width=True)
    
    # Best model info
    best_model = comparison_df.loc[comparison_df['K-Fold Mean Accuracy'].idxmax(), 'Model']
    best_accuracy = comparison_df['K-Fold Mean Accuracy'].max()
    
    st.success(f"🏆 Best Model: **{best_model}** with {best_accuracy:.4f} accuracy")
    
    # Feature importance
    if hasattr(st.session_state.engine, 'get_feature_importance'):
        st.markdown("### 🎯 Feature Importance")
        importance_df = st.session_state.engine.get_feature_importance()
        
        if importance_df is not None:
            fig = px.bar(importance_df.head(10), x='Importance', y='Feature', 
                        orientation='h', color='Importance', 
                        color_continuous_scale='viridis')
            fig.update_layout(height=500, yaxis={'categoryorder':'total ascending'})
            st.plotly_chart(fig, use_container_width=True)

def show_visualizations_page():
    """Show data visualizations"""
    st.markdown("## 📈 Data Visualizations")
    
    processor = st.session_state.processor
    visualizer = LaptopDataVisualizer(processor)
    
    # Interactive charts
    charts = visualizer.create_interactive_plotly_charts()
    
    st.markdown("### 🎯 Performance vs Price Analysis")
    st.plotly_chart(charts['performance_vs_price'], use_container_width=True)
    
    st.markdown("### 🏢 Brand Comparison")
    st.plotly_chart(charts['brand_comparison'], use_container_width=True)
    
    st.markdown("### 🌟 Category Breakdown")
    st.plotly_chart(charts['category_breakdown'], use_container_width=True)
    
    # Additional analysis
    df = processor.processed_df
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 💾 Storage vs Price")
        fig = px.scatter(df, x='Storage', y='Final Price', color='Usage_Category',
                        size='RAM', hover_data=['Brand', 'Model'])
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("### 🧠 RAM vs Performance")
        fig = px.scatter(df, x='RAM', y='Performance_Score', color='Has_Dedicated_GPU',
                        hover_data=['Brand', 'Model', 'Final Price'])
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()
