import streamlit as st
import pandas as pd

# ====== إعدادات الصفحة ======
st.set_page_config(page_title="ترشيح اللابتوبات", layout="wide")

# ====== تنسيقات CSS مخصصة للألوان ======
st.markdown("""
    <style>
        body {
            background-color: #F0EFED;
        }

        .title {
            font-size: 36px;
            color: #32127A;
            font-weight: bold;
        }

        .subtitle {
            font-size: 20px;
            color: #B3446C;
        }

        .laptop-card {
            background: linear-gradient(to bottom right, #CDE0E1, #E3CCDC);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 2px 2px 12px rgba(0,0,0,0.1);
        }

        .filter-box {
            background-color: #E3CCDC;
            padding: 15px;
            border-radius: 10px;
        }

        .gradient-button {
            background: linear-gradient(to right, #F0EFED, #B3446C, #32127A);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
        }

        .gradient-button:hover {
            opacity: 0.9;
        }
    </style>
""", unsafe_allow_html=True)

# ====== العنوان ======
st.markdown('<div class="title">🎯 اختر أفضل لابتوب يناسب استخدامك</div>', unsafe_allow_html=True)
st.markdown('<div class="subtitle">موقع ذكي يساعدك على الوصول لأفضل لابتوب بسهولة</div>', unsafe_allow_html=True)
st.write("")

# ====== الفلتر ======
with st.sidebar:
    st.markdown('<div class="filter-box">', unsafe_allow_html=True)
    st.header("⚙️ الفلاتر")
    purpose = st.selectbox("استخدامك الأساسي:", ["برمجة", "الدراسة", "تصميم", "ألعاب", "استخدام عام"])
    price_range = st.slider("السعر (بالدولار):", 300, 3000, (800, 1500))
    st.markdown('</div>', unsafe_allow_html=True)

# ====== زر الترشيح ======
if st.button("🔍 ابدأ الترشيح", help="اضغط لعرض الترشيحات"):
    st.markdown('<br>', unsafe_allow_html=True)

    # عرض 3 كروت كأمثلة
    col1, col2, col3 = st.columns(3)
    with col1:
        st.markdown('<div class="laptop-card">', unsafe_allow_html=True)
        st.image("https://via.placeholder.com/250", caption="Lenovo IdeaPad 5")
        st.write("💻 معالج: Ryzen 5\n\n🎓 مناسب: برمجة")
        st.markdown('</div>', unsafe_allow_html=True)
    with col2:
        st.markdown('<div class="laptop-card">', unsafe_allow_html=True)
        st.image("https://via.placeholder.com/250", caption="Dell Inspiron 15")
        st.write("💻 معالج: Intel i5\n\n🎮 مناسب: ألعاب خفيفة")
        st.markdown('</div>', unsafe_allow_html=True)
    with col3:
        st.markdown('<div class="laptop-card">', unsafe_allow_html=True)
        st.image("https://via.placeholder.com/250", caption="MacBook Air M1")
        st.write("💻 معالج: Apple M1\n\n🎨 مناسب: تصميم ودراسة")
        st.markdown('</div>', unsafe_allow_html=True)

