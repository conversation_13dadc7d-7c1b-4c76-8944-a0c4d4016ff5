        
        duplicate_count = st.session_state.cleaned_data.duplicated().sum()
        
        if duplicate_count == 0:
            st.success("✅ No duplicate rows found!")
            if st.button("➡️ Proceed to Feature Encoding", key="step4_next"):
                st.session_state.cleaning_step = 5
                st.rerun()
        else:
            st.warning(f"⚠️ Found {duplicate_count} duplicate rows")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**Duplicate Detection Options:**")
                check_subset = st.checkbox("Check specific columns only")
                
                if check_subset:
                    subset_cols = st.multiselect(
                        "Select columns to check:",
                        st.session_state.cleaned_data.columns.tolist(),
                        default=st.session_state.cleaned_data.columns.tolist()[:3]
                    )
                else:
                    subset_cols = None
            
            with col2:
                if st.button("🗑️ Remove Duplicates", key="remove_duplicates"):
                    before_count = len(st.session_state.cleaned_data)
                    if subset_cols:
                        st.session_state.cleaned_data = st.session_state.cleaned_data.drop_duplicates(subset=subset_cols)
                    else:
                        st.session_state.cleaned_data = st.session_state.cleaned_data.drop_duplicates()
                    
                    after_count = len(st.session_state.cleaned_data)
                    removed_count = before_count - after_count
                    
                    st.session_state.cleaning_history.append(f"Removed {removed_count} duplicate rows")
                    st.success(f"✅ Removed {removed_count} duplicate rows")
                    st.rerun()
            
            if st.button("➡️ Proceed to Feature Encoding", key="step4_next"):
                st.session_state.cleaning_step = 5
                st.rerun()

    # Step 5: Feature Encoding
    if st.session_state.cleaning_step >= 5:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.2rem; border-radius: 12px; margin: 1.5rem 0;">
            <h4 style="color: #32127A; margin-bottom: 0.8rem; font-weight: 700;">📝 Step 5: Feature Encoding</h4>
        </div>
        """, unsafe_allow_html=True)
        
        categorical_cols = st.session_state.cleaned_data.select_dtypes(include=['object']).columns.tolist()
        
        if len(categorical_cols) == 0:
            st.success("✅ No categorical columns found!")
            if st.button("➡️ Proceed to Feature Scaling", key="step5_next"):
                st.session_state.cleaning_step = 6
                st.rerun()
        else:
            st.info(f"📝 Found {len(categorical_cols)} categorical columns: {', '.join(categorical_cols)}")
            
            col1, col2 = st.columns(2)
            
            with col1:
                selected_cols = st.multiselect(
                    "Select columns to encode:",
                    categorical_cols,
                    default=categorical_cols
                )
                
                encoding_method = st.radio(
                    "Choose encoding method:",
                    ["Label Encoding", "One-Hot Encoding"]
                )
            
            with col2:
                if selected_cols and st.button("🔄 Apply Encoding", key="apply_encoding"):
                    for col in selected_cols:
                        if encoding_method == "Label Encoding":
                            label_encoder = LabelEncoder()
                            st.session_state.cleaned_data[col] = label_encoder.fit_transform(st.session_state.cleaned_data[col].astype(str))
                        else:  # One-Hot Encoding
                            # Create dummy variables
                            dummies = pd.get_dummies(st.session_state.cleaned_data[col], prefix=col)
                            st.session_state.cleaned_data = pd.concat([st.session_state.cleaned_data.drop(col, axis=1), dummies], axis=1)
                    
                    st.session_state.cleaning_history.append(f"Applied {encoding_method} to {len(selected_cols)} columns")
                    st.success(f"✅ Applied {encoding_method} to {len(selected_cols)} columns")
                    st.rerun()
            
            if st.button("➡️ Proceed to Feature Scaling", key="step5_next"):
                st.session_state.cleaning_step = 6
                st.rerun()

    # Step 6: Feature Scaling
    if st.session_state.cleaning_step >= 6:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.2rem; border-radius: 12px; margin: 1.5rem 0;">
            <h4 style="color: #32127A; margin-bottom: 0.8rem; font-weight: 700;">📏 Step 6: Feature Scaling</h4>
        </div>
        """, unsafe_allow_html=True)
        
        numeric_cols = st.session_state.cleaned_data.select_dtypes(include=['int64', 'float64']).columns.tolist()
        
        if len(numeric_cols) == 0:
            st.success("✅ No numeric columns found for scaling!")
        else:
            st.info(f"📏 Found {len(numeric_cols)} numeric columns for scaling")
            
            col1, col2 = st.columns(2)
            
            with col1:
                selected_cols = st.multiselect(
                    "Select columns to scale:",
                    numeric_cols,
                    default=numeric_cols
                )
                
                scaling_method = st.radio(
                    "Choose scaling method:",
                    ["StandardScaler", "MinMaxScaler"]
                )
            
            with col2:
                if selected_cols and st.button("📊 Apply Scaling", key="apply_scaling"):
                    if scaling_method == "StandardScaler":
                        scaler = StandardScaler()
                    else:
                        from sklearn.preprocessing import MinMaxScaler
                        scaler = MinMaxScaler()
                    
                    st.session_state.cleaned_data[selected_cols] = scaler.fit_transform(st.session_state.cleaned_data[selected_cols])
                    
                    st.session_state.cleaning_history.append(f"Applied {scaling_method} to {len(selected_cols)} columns")
                    st.success(f"✅ Applied {scaling_method} to {len(selected_cols)} columns")
                    st.rerun()
        
        # Final step completion
        st.markdown("""
        <div style="background: linear-gradient(135deg, #32127A 0%, #008292 100%); padding: 2rem; border-radius: 15px; margin: 2rem 0; text-align: center; color: white;">
            <h3 style="margin-bottom: 1rem;">🎉 Data Cleaning Complete!</h3>
            <p style="margin-bottom: 1.5rem; opacity: 0.9;">Your dataset has been successfully cleaned and preprocessed</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Final data summary
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("📊 Final Rows", len(st.session_state.cleaned_data))
        with col2:
            st.metric("📋 Final Columns", len(st.session_state.cleaned_data.columns))
        with col3:
            missing_final = st.session_state.cleaned_data.isnull().sum().sum()
            st.metric("✅ Missing Values", missing_final)
        
        # Cleaning history
        if st.session_state.cleaning_history:
            st.markdown("**📋 Cleaning History:**")
            for i, step in enumerate(st.session_state.cleaning_history, 1):
                st.write(f"{i}. {step}")
        
        # Save final data
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 Save Cleaned Data", key="save_final"):
                st.session_state.cleaned_data.to_csv("cleaned_laptops.csv", index=False)
                st.success("✅ Cleaned data saved as 'cleaned_laptops.csv'")
        
        with col2:
            if st.button("🔄 Reset Cleaning Process", key="reset_cleaning"):
                st.session_state.cleaning_step = 1
                st.session_state.cleaned_data = data.copy()
                st.session_state.cleaning_history = []
                st.success("✅ Cleaning process reset!")
                st.rerun()
        
        # Final data preview
        st.markdown("**Final Cleaned Data Preview:**")
        st.dataframe(st.session_state.cleaned_data.head(), use_container_width=True)
