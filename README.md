# 🔥 Smart Laptop Recommender

A comprehensive data science project featuring an AI-powered laptop recommendation system with modern glassmorphism UI design.

## 🎯 Project Overview

This project demonstrates a complete data science pipeline including:
- **Data preprocessing and feature engineering**
- **Multiple ML model evaluation (train-test vs k-fold comparison)**
- **Interactive recommendation system**
- **Modern glassmorphism UI design**
- **Advanced data visualizations**

## ✨ Features

### 🏠 Smart Recommendation Engine
- **AI-powered recommendations** based on user preferences
- **Usage-based filtering** (Gaming, Graphics, Studying)
- **Budget optimization** with intelligent scoring
- **Performance and value analysis**
- **Purchase links integration**

### 🤖 Advanced ML Pipeline
- **Multiple algorithms comparison**:
  - K-Nearest Neighbors
  - Random Forest
  - Gradient Boosting
  - Support Vector Machine
  - Logistic Regression
- **Train-test split vs K-fold cross-validation**
- **Comprehensive performance metrics**
- **Feature importance analysis**

### 📊 Interactive Data Analysis
- **Enhanced data visualizations** with matplotlib/seaborn
- **Interactive Plotly charts**
- **Brand performance comparison**
- **Usage category analysis**
- **Correlation heatmaps**

### 🎨 Modern UI Design
- **Glassmorphism design patterns**
- **Responsive layout**
- **Interactive components**
- **Professional styling**

## 🚀 Quick Start

### Prerequisites
```bash
pip install streamlit pandas numpy matplotlib seaborn plotly scikit-learn
```

### Running the Application
```bash
# Method 1: Direct launch
streamlit run Final_DA_Project.py

# Method 2: Using the launcher
python run_app.py
```

## 📁 Project Structure

```
DA_Project/
├── Final_DA_Project.py    # Main application
├── laptops.csv           # Dataset
├── run_app.py            # Application launcher
├── README.md             # This file
└── requirements.txt      # Dependencies
```

## 🔧 Technical Implementation

### Data Processing Pipeline
1. **Data Loading**: CSV import with error handling
2. **Feature Engineering**: 
   - CPU performance categorization
   - Usage type classification
   - Weight categories
   - Performance scoring
   - Value analysis
3. **Data Cleaning**: Missing value handling, outlier detection

### Machine Learning Pipeline
1. **Data Preparation**: Feature selection and encoding
2. **Model Training**: Multiple algorithm implementation
3. **Evaluation**: Comprehensive metrics comparison
4. **Recommendation Logic**: Smart scoring algorithm

### UI Components
1. **Glassmorphism Styling**: Modern CSS with backdrop filters
2. **Interactive Forms**: User preference collection
3. **Dynamic Visualizations**: Real-time chart updates
4. **Responsive Design**: Mobile-friendly layout

## 📈 Model Performance

The system evaluates multiple ML models and provides:
- **Accuracy scores** for each algorithm
- **Precision, Recall, F1-score** metrics
- **Cross-validation results** with confidence intervals
- **Feature importance** analysis
- **Best model recommendations**

## 🎮 Usage Examples

### Gaming Laptop Recommendation
```python
preferences = {
    'usage_type': 'Gaming',
    'budget_min': 1000,
    'budget_max': 1500,
    'weight_preference': 'Any',
    'preferred_brands': ['MSI', 'ASUS']
}
```

### Study Laptop Recommendation
```python
preferences = {
    'usage_type': 'Studying',
    'budget_min': 300,
    'budget_max': 800,
    'weight_preference': 'Light',
    'preferred_brands': []
}
```

## 📊 Dataset Information

The project uses a comprehensive laptop dataset with:
- **2000+ laptop entries**
- **12 key features** (Brand, Model, CPU, RAM, Storage, etc.)
- **Price range**: €239 - €2249
- **Multiple brands**: ASUS, HP, Lenovo, MSI, Acer, etc.

## 🔍 Key Insights

### Performance Analysis
- **Gaming laptops** average 85+ performance score
- **Graphics workstations** focus on CPU and GPU power
- **Study laptops** optimize for value and portability

### Price Trends
- **Best value range**: €500-€1000
- **Premium segment**: €1500+ with high-end specs
- **Budget options**: €300-€500 for basic needs

### Brand Comparison
- **MSI**: Gaming-focused, high performance
- **ASUS**: Balanced portfolio, good value
- **HP**: Business-oriented, reliable
- **Lenovo**: Enterprise solutions, durability

## 🛠️ Customization

### Adding New Features
1. Update `feature_engineering()` function
2. Modify recommendation scoring algorithm
3. Add new visualization components

### Styling Modifications
1. Edit CSS in the `st.markdown()` sections
2. Customize color schemes
3. Add new glassmorphism effects

## 📝 Future Enhancements

- [ ] **Real-time price updates** from e-commerce APIs
- [ ] **User rating system** for recommendations
- [ ] **Advanced filtering** (screen resolution, battery life)
- [ ] **Comparison tool** for side-by-side analysis
- [ ] **Export functionality** for recommendation reports

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 👨‍💻 Author

Created as a comprehensive data science portfolio project demonstrating:
- **Machine Learning expertise**
- **Data visualization skills**
- **UI/UX design capabilities**
- **Full-stack development**

## 🔗 Links

- **LinkedIn**: [Your LinkedIn Profile]
- **Portfolio**: [Your Portfolio Website]
- **GitHub**: [Your GitHub Profile]

---

**⭐ If you found this project helpful, please give it a star!**
