# Proper ending for tab3
                st.rerun()

# Continue with other tabs (tab4, tab5)
with tab4:
    st.title("🤖 Model Evaluation")
    st.write("📊 Evaluate different machine learning models for laptop recommendation.")
    
    # Check if cleaned data is available
    if 'model_ready_data' in st.session_state and st.session_state.get('data_cleaning_complete', False):
        st.success("✅ Using cleaned data from Data Cleaning Process")
        model_data = st.session_state.model_ready_data
    else:
        st.info("ℹ️ Using original data. Complete data cleaning process for better results.")
        model_data = data
    
    # Model evaluation content here...
    st.dataframe(model_data.head(), use_container_width=True)

with tab5:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.8rem; margin-bottom: 0.5rem; font-weight: 700;">💻 Recommend me a laptop!</h1>
        <p style="font-size: 1.3rem; opacity: 0.9; margin: 0;">Smart AI-powered system to find your perfect laptop based on your needs</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Rest of tab5 content...
