#!/usr/bin/env python3
"""
Quick launcher for the Smart Laptop Recommender
"""

import subprocess
import sys
import os

def main():
    """Launch the Streamlit application"""
    print("🔥 Starting Smart Laptop Recommender...")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("Final_DA_Project.py"):
        print("❌ Error: Final_DA_Project.py not found!")
        print("Please run this script from the project directory.")
        return
    
    if not os.path.exists("laptops.csv"):
        print("❌ Error: laptops.csv not found!")
        print("Please ensure the dataset is in the project directory.")
        return
    
    try:
        # Launch Streamlit
        print("🚀 Launching Streamlit application...")
        print("📱 The app will open in your default browser")
        print("🛑 Press Ctrl+C to stop the application")
        print("=" * 50)
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "Final_DA_Project.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error launching application: {e}")
        print("💡 Try running: streamlit run Final_DA_Project.py")

if __name__ == "__main__":
    main()
