import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler
import re

class LaptopDataProcessor:
    def __init__(self, csv_path='laptops.csv'):
        self.csv_path = csv_path
        self.df = None
        self.processed_df = None
        self.label_encoders = {}
        self.scaler = StandardScaler()
        
    def load_data(self):
        """Load the laptop dataset"""
        self.df = pd.read_csv(self.csv_path)
        print(f"Dataset loaded: {self.df.shape[0]} laptops, {self.df.shape[1]} features")
        return self.df
    
    def clean_data(self):
        """Clean and preprocess the data"""
        df = self.df.copy()
        
        # Handle missing values
        print("Handling missing values...")
        
        # GPU: NaN means integrated graphics
        df['GPU'] = df['GPU'].fillna('Integrated Graphics')
        
        # Storage type: Fill missing with most common type
        df['Storage type'] = df['Storage type'].fillna(df['Storage type'].mode()[0])
        
        # Screen: Fill with median
        df['Screen'] = df['Screen'].fillna(df['Screen'].median())
        
        print(f"Missing values after cleaning: {df.isnull().sum().sum()}")
        
        self.df = df
        return df
    
    def extract_cpu_performance(self, cpu_string):
        """Extract CPU performance tier from CPU string"""
        cpu_lower = cpu_string.lower()
        
        # Intel processors
        if 'i7' in cpu_lower or 'i9' in cpu_lower:
            return 'High Performance'
        elif 'i5' in cpu_lower:
            return 'Mid Performance'
        elif 'i3' in cpu_lower:
            return 'Basic Performance'
        elif 'celeron' in cpu_lower or 'pentium' in cpu_lower:
            return 'Entry Level'
        
        # AMD processors
        elif 'ryzen 7' in cpu_lower or 'ryzen 9' in cpu_lower:
            return 'High Performance'
        elif 'ryzen 5' in cpu_lower:
            return 'Mid Performance'
        elif 'ryzen 3' in cpu_lower:
            return 'Basic Performance'
        
        return 'Basic Performance'
    
    def categorize_usage(self, row):
        """Categorize laptop usage based on specs"""
        gpu = str(row['GPU']).lower()
        cpu_perf = row['CPU_Performance']
        ram = row['RAM']
        
        # Gaming laptops: Dedicated GPU + good CPU + sufficient RAM
        if ('rtx' in gpu or 'gtx' in gpu or 'radeon' in gpu) and ram >= 16:
            if cpu_perf in ['High Performance', 'Mid Performance']:
                return 'Gaming'
        
        # Graphics/Creative work: Good CPU + RAM, may have dedicated GPU
        if cpu_perf == 'High Performance' and ram >= 16:
            return 'Graphics'
        
        # Everything else is suitable for studying/office work
        return 'Studying'
    
    def categorize_weight(self, screen_size):
        """Categorize weight based on screen size (proxy)"""
        if screen_size <= 13.3:
            return 'Light'
        elif screen_size <= 15.6:
            return 'Medium'
        else:
            return 'Heavy'
    
    def feature_engineering(self):
        """Create new features for better recommendations"""
        df = self.df.copy()
        
        print("Creating new features...")
        
        # CPU Performance tier
        df['CPU_Performance'] = df['CPU'].apply(self.extract_cpu_performance)
        
        # Usage category
        df['Usage_Category'] = df.apply(self.categorize_usage, axis=1)
        
        # Weight category
        df['Weight_Category'] = df['Screen'].apply(self.categorize_weight)
        
        # Has dedicated GPU
        df['Has_Dedicated_GPU'] = df['GPU'].apply(lambda x: 0 if 'integrated' in str(x).lower() else 1)
        
        # Storage per dollar
        df['Storage_Per_Dollar'] = df['Storage'] / df['Final Price']
        
        # RAM per dollar
        df['RAM_Per_Dollar'] = df['RAM'] / df['Final Price']
        
        # Performance score (simple heuristic)
        cpu_scores = {'Entry Level': 1, 'Basic Performance': 2, 'Mid Performance': 3, 'High Performance': 4}
        df['CPU_Score'] = df['CPU_Performance'].map(cpu_scores)
        
        df['Performance_Score'] = (
            df['CPU_Score'] * 0.3 + 
            (df['RAM'] / 32) * 0.3 + 
            (df['Storage'] / 1000) * 0.2 + 
            df['Has_Dedicated_GPU'] * 0.2
        )
        
        # Value score (performance per dollar)
        df['Value_Score'] = df['Performance_Score'] / (df['Final Price'] / 1000)
        
        self.processed_df = df
        print(f"Feature engineering complete. New features added.")
        return df
    
    def prepare_for_ml(self):
        """Prepare data for machine learning"""
        df = self.processed_df.copy()
        
        # Select features for ML
        categorical_features = ['Brand', 'CPU_Performance', 'Storage type', 'Usage_Category', 'Weight_Category']
        numerical_features = ['RAM', 'Storage', 'Screen', 'Final Price', 'Has_Dedicated_GPU', 
                            'Performance_Score', 'Value_Score']
        
        # Encode categorical variables
        for feature in categorical_features:
            le = LabelEncoder()
            df[f'{feature}_encoded'] = le.fit_transform(df[feature])
            self.label_encoders[feature] = le
        
        # Prepare feature matrix
        feature_columns = [f'{f}_encoded' for f in categorical_features] + numerical_features
        X = df[feature_columns]
        
        # Target variable (Usage Category)
        y = df['Usage_Category_encoded']
        
        return X, y, df
    
    def get_recommendations(self, usage_type, budget_min, budget_max, weight_preference, top_n=5):
        """Get laptop recommendations based on criteria"""
        df = self.processed_df.copy()
        
        # Filter by budget
        filtered_df = df[(df['Final Price'] >= budget_min) & (df['Final Price'] <= budget_max)]
        
        # Filter by usage type if specified
        if usage_type != 'Any':
            filtered_df = filtered_df[filtered_df['Usage_Category'] == usage_type]
        
        # Filter by weight preference if specified
        if weight_preference != 'Any':
            filtered_df = filtered_df[filtered_df['Weight_Category'] == weight_preference]
        
        if filtered_df.empty:
            return pd.DataFrame()
        
        # Sort by value score (best value for money)
        recommendations = filtered_df.nlargest(top_n, 'Value_Score')
        
        return recommendations[['Laptop', 'Brand', 'Model', 'CPU', 'RAM', 'Storage', 'GPU', 
                              'Screen', 'Final Price', 'Usage_Category', 'Weight_Category', 
                              'Performance_Score', 'Value_Score']]
    
    def get_data_summary(self):
        """Get summary statistics for the dashboard"""
        if self.processed_df is None:
            return {}
        
        df = self.processed_df
        
        summary = {
            'total_laptops': len(df),
            'price_range': (df['Final Price'].min(), df['Final Price'].max()),
            'avg_price': df['Final Price'].mean(),
            'brands': df['Brand'].nunique(),
            'usage_distribution': df['Usage_Category'].value_counts().to_dict(),
            'weight_distribution': df['Weight_Category'].value_counts().to_dict(),
            'gpu_distribution': df['Has_Dedicated_GPU'].value_counts().to_dict()
        }
        
        return summary

if __name__ == "__main__":
    # Test the processor
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()
    
    print("\nData Summary:")
    summary = processor.get_data_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    print("\nSample recommendations for Gaming, $1000-$1500, Any weight:")
    recs = processor.get_recommendations('Gaming', 1000, 1500, 'Any')
    print(recs[['Laptop', 'Final Price', 'Performance_Score', 'Value_Score']].head())
