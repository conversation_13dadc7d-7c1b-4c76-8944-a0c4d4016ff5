import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import io
from sklearn.experimental import enable_iterative_imputer
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, OneHotEncoder, StandardScaler, MinMaxScaler, PowerTransformer
from sklearn.impute import SimpleImputer, KNNImputer, IterativeImputer
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.svm import SVC, SVR
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, 
                           confusion_matrix, classification_report, roc_curve, auc,
                           mean_squared_error, mean_absolute_error, r2_score, silhouette_score)
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.cluster import KMeans
from imblearn.over_sampling import SMOTE
from scipy.stats.mstats import winsorize
from sklearn.decomposition import PCA
from sklearn.ensemble import IsolationForest  
from sklearn.cluster import DBSCAN
from scipy import stats
import warnings
from pandas.api.types import is_numeric_dtype
from sklearn.feature_selection import RFE

warnings.filterwarnings("ignore")

# ====== Page Config ======
st.set_page_config(
    page_title="ML App",
    layout="wide",
    page_icon="🧠",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    /* Modify main navigation tabs */
    .stTabs [data-baseweb="tab-list"] {
        background-color: #0E1117 !important;
        border-bottom: 1px solid #30363D !important;
        gap: 0.5rem;
    }
    
    /* Inactive tab */
    .stTabs [data-baseweb="tab"] {
        background-color: #161B22 !important;
        color: #8B949E !important;
        border: 1px solid #30363D !important;
        border-bottom: none !important;
        padding: 0.5rem 1rem;
        border-radius: 4px 4px 0 0 !important;
        margin-right: 0 !important;
    }
    
    /* Active tab */
    .stTabs [aria-selected="true"] {
        background-color: #0E1117 !important;
        color: #58A6FF !important;
        border-color: #30363D !important;
        border-bottom: 1px solid #0E1117 !important;
        font-weight: bold;
    }
    
    /* Tab content */
    .stTabs [role="tabpanel"] {
        background-color: #0E1117 !important;
        border: 1px solid #30363D !important;
        border-top: none !important;
        padding: 1rem;
        border-radius: 0 0 4px 4px !important;
    }
    
    /* Hover effects on tabs */
    .stTabs [data-baseweb="tab"]:hover {
        background-color: #1F2937 !important;
        color: #E0E0E0 !important;
    }
</style>
""", unsafe_allow_html=True)

# ====== Session State Initialization ======
if 'df' not in st.session_state:
    st.session_state.df = None
if 'processed_df' not in st.session_state:
    st.session_state.processed_df = None
if 'model' not in st.session_state:
    st.session_state.model = None
if 'X_train' not in st.session_state:
    st.session_state.X_train = None
if 'X_test' not in st.session_state:
    st.session_state.X_test = None
if 'y_train' not in st.session_state:
    st.session_state.y_train = None
if 'y_test' not in st.session_state:
    st.session_state.y_test = None
if 'y_pred' not in st.session_state:
    st.session_state.y_pred = None
if 'feature_columns' not in st.session_state:
    st.session_state.feature_columns = None
if 'task_type' not in st.session_state:
    st.session_state.task_type = None
if 'learning_type' not in st.session_state:
    st.session_state.learning_type = "Supervised Learning"  # Default value

# ====== Helper Functions ======
@st.cache_data
def load_data(uploaded_file):
    return pd.read_csv(uploaded_file)

def handle_missing_values(df_copy, method, selected_cols, strategy=None, fill_value=None):
    try:
        if method == "Simple Imputer":
            imputer = SimpleImputer(strategy=strategy, fill_value=fill_value)
        elif method == "KNN Imputer":
            imputer = KNNImputer(n_neighbors=3)
        elif method == "Iterative Imputer":
            imputer = IterativeImputer(max_iter=10, random_state=42)
        else:
            return df_copy
        
        if selected_cols:
            df_copy[selected_cols] = imputer.fit_transform(df_copy[selected_cols])
        return df_copy
    except Exception as e:
        st.error(f"Error in missing value handling: {str(e)}")
        return df_copy

def encode_data(df_copy, method, cat_cols):
    try:
        if not cat_cols:
            return df_copy
            
        if method == "Label Encoding":
            le = LabelEncoder()
            for col in cat_cols:
                df_copy[col] = le.fit_transform(df_copy[col].astype(str))
        elif method == "One-Hot Encoding":
            ohe = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
            ohe_encoded = ohe.fit_transform(df_copy[cat_cols])
            ohe_df = pd.DataFrame(ohe_encoded, columns=ohe.get_feature_names_out(cat_cols))
            df_copy = df_copy.drop(cat_cols, axis=1)
            df_copy = pd.concat([df_copy, ohe_df], axis=1)
        return df_copy
    except Exception as e:
        st.error(f"Error in encoding: {str(e)}")
        return df_copy

def scale_features(df_copy, method, num_cols):
    try:
        if not num_cols:
            return df_copy
            
        if method == "Standard Scaler":
            scaler = StandardScaler()
        elif method == "MinMax Scaler":
            scaler = MinMaxScaler()
        else:
            return df_copy
            
        df_copy[num_cols] = scaler.fit_transform(df_copy[num_cols])
        return df_copy
    except Exception as e:
        st.error(f"Error in scaling: {str(e)}")
        return df_copy

def train_model(model_choice, X_train, y_train=None, **params):
    try:
        if model_choice == "LogisticRegression":
            model = LogisticRegression(**params)
        elif model_choice == "DecisionTree":
            model = DecisionTreeClassifier(**params)
        elif model_choice == "RandomForest":
            model = RandomForestClassifier(**params) if y_train is not None else RandomForestRegressor(**params)
        elif model_choice == "SVM":
            model = SVC(probability=True, **params)
        elif model_choice == "KNN":
            model = KNeighborsClassifier(**params)
        elif model_choice == "LinearRegression":
            model = LinearRegression(**params)
        elif model_choice == "SVR":
            model = SVR(**params)
        elif model_choice == "KMeans":
            model = KMeans(**params)
        model.fit(X_train, y_train if y_train is not None else None)
        return model
    except Exception as e:
        st.error(f"Error in model training: {str(e)}")
        return None

def detect_task_type(df, target_column):
    if st.checkbox("Manually select task type?", key="manual_task"):
        return st.selectbox("Select task type:", ["classification", "regression", "clustering"])
    if target_column == "None" or target_column is None:
        return "clustering"
    if is_numeric_dtype(df[target_column]) and df[target_column].nunique() > 10:
        return "regression"
    return "classification"

def prepare_data(df, target_column):
    if len(df) < 10:
        raise ValueError("Too few rows to train the model!")
    
    if df.isnull().any().any():
        raise ValueError("Data contains missing values. Please handle them in the preprocessing step.")
    
    if target_column == "None":
        X = pd.get_dummies(df)
        X_train, X_test = train_test_split(X, test_size=0.2, random_state=42)
        return (X_train, X_test, None, None), X.columns.tolist()
    
    X = df.drop(columns=[target_column])
    y = df[target_column]
    
    if y.dtype == object:
        le = LabelEncoder()
        y = le.fit_transform(y)

    X = pd.get_dummies(X)
    return train_test_split(X, y, test_size=0.2, random_state=42), X.columns.tolist()

def get_models(task, learning_type):
    if learning_type == "Supervised Learning":
        if task == "classification":
            return {
                "LogisticRegression": LogisticRegression(),
                "DecisionTree": DecisionTreeClassifier(),
                "RandomForest": RandomForestClassifier(),
                "SVM": SVC(probability=True),
                "KNN": KNeighborsClassifier()
            }
        elif task == "regression":
            return {
                "LinearRegression": LinearRegression(),
                "RandomForest": RandomForestRegressor(),
                "SVR": SVR()
            }
    else:  # Unsupervised Learning
        return {
            "KMeans": KMeans(n_clusters=3, random_state=42)
        }

def get_metrics(task):
    if task == "classification":
        return {
            "Accuracy": accuracy_score,
            "F1 Score": f1_score,
            "Precision": precision_score,
            "Recall": recall_score
        }
    elif task == "regression":
        return {
            "MSE": mean_squared_error,
            "MAE": mean_absolute_error,
            "R2": r2_score
        }
    else:  # clustering
        return {
            "Silhouette Score": silhouette_score
        }

def train_single_model(model, X_train, y_train=None):
    model.fit(X_train, y_train if y_train is not None else X_train)
    return model

def evaluate_model(model, X_test, y_test=None, metric_func=None, task_type=None):
    if task_type == "clustering":
        y_pred = model.predict(X_test)
        try:
            return metric_func(X_test, y_pred)
        except Exception as e:
            st.error(f"Error calculating silhouette score: {str(e)}")
            return None
    y_pred = model.predict(X_test)
    try:
        if task_type == "classification":
            metrics_with_average = [f1_score, precision_score, recall_score]
            if metric_func in metrics_with_average:
                if len(np.unique(y_test)) > 2:
                    return metric_func(y_test, y_pred, average="weighted")
                else:
                    return metric_func(y_test, y_pred)
            else:
                return metric_func(y_test, y_pred)
        else:
            return metric_func(y_test, y_pred)
    except ValueError as e:
        st.error(f"Error calculating metric: {str(e)}. Try a different metric or check your data.")
        return None
    except Exception as e:
        st.error(f"Unexpected error: {str(e)}")
        return None

def save_model(model, feature_columns, filename="best_model.pkl"):
    with open(filename, "wb") as f:
        pickle.dump(model, f)
    with open("feature_columns.pkl", "wb") as f:
        pickle.dump(feature_columns, f)

def load_model(filename="best_model.pkl"):
    with open(filename, "rb") as f:
        model = pickle.load(f)
    with open("feature_columns.pkl", "rb") as f:
        feature_columns = pickle.load(f)
    return model, feature_columns

def predict_new_data(model, feature_columns, new_df):
    new_data = pd.get_dummies(new_df)
    for col in feature_columns:
        if col not in new_data.columns:
            new_data[col] = 0
    new_data = new_data[feature_columns]
    return model.predict(new_data)

# ====== Main App ======
st.title("🧠 Machine Learning App")
st.markdown("""
    A complete workflow for data preprocessing, visualization, model training, and evaluation.
    Follow the steps sequentially from left to right.
""")

# ====== Tabs ======
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "📁 Upload Data", 
    "🔧 Preprocessing", 
    "📊 Visualization", 
    "🤖 Model Training", 
    "📈 Evaluation"
])

# ====== Upload Data Tab ======
with tab1:
    st.header("1. Upload Your Dataset")
    st.info("Start by uploading your dataset in CSV format")
    
    uploaded_file = st.file_uploader(
        "Choose a CSV file", 
        type=["csv"],
        accept_multiple_files=False,
        key="file_uploader"
    )
    
    if uploaded_file is not None:
        with st.spinner("Loading data..."):
            st.session_state.df = load_data(uploaded_file)
            st.success("✅ Data loaded successfully!")
            
            col1, col2 = st.columns(2)
            with col1:
                st.subheader("Data Preview")
                st.dataframe(st.session_state.df.head())
            with col2:
                st.subheader("Data Summary")
                buffer = io.StringIO()
                st.session_state.df.info(buf=buffer)
                st.text(buffer.getvalue())
                
            st.subheader("Quick Stats")
            st.dataframe(st.session_state.df.describe())

# ====== Preprocessing Tab ======
with tab2:
    st.header("2. Data Preprocessing")
    
    if st.session_state.df is None:
        st.warning("Please upload data first in the 'Upload Data' tab.")
    else:
        if st.session_state.processed_df is None:
            st.session_state.processed_df = st.session_state.df.copy()
        
        df_copy = st.session_state.processed_df.copy()
        
        sub_tab1, sub_tab2, sub_tab3, sub_tab4, sub_tab5 = st.tabs([
            "🔍 Handle Missing Values", 
            "🔠 Encode Categorical Variables", 
            "🚨 Handle Outliers",         
            "⚖ Feature Scaling",            
            "📉 Feature Selection"
        ])
        
        # Handle Missing Values
        with sub_tab1:
            st.subheader("Handle Missing Values")
            
            missing_cols = df_copy.columns[df_copy.isnull().any()].tolist()
            if missing_cols:
                st.info(f"Columns with missing values: {', '.join(missing_cols)}")
            else:
                st.success("No missing values detected.")
            
            missing_option = st.selectbox(
                "Select imputation method:",
                ["None", "Simple Imputer", "KNN Imputer", "Iterative Imputer"],
                key="missing_option"
            )
            
            if missing_option != "None":
                selected_cols_missing = st.multiselect(
                    "Select columns to impute:",
                    missing_cols if missing_cols else df_copy.columns,
                    key="missing_cols"
                )
                
                if missing_option == "Simple Imputer":
                    strategy = st.selectbox(
                        "Imputation strategy:",
                        ["mean", "median", "most_frequent", "constant"],
                        key="strategy"
                    )
                    if strategy == "constant":
                        fill_value = st.text_input(
                            "Constant value to fill:",
                            value="0",
                            key="fill_value"
                        )
            
            if st.button("Apply Missing Value Handling", key="apply_missing"):
                with st.spinner("Handling missing values..."):
                    df_copy = handle_missing_values(
                        df_copy, 
                        missing_option, 
                        selected_cols_missing if 'selected_cols_missing' in locals() else [],
                        strategy if 'strategy' in locals() else None,
                        fill_value if 'fill_value' in locals() else None
                    )
                    st.success("✅ Missing values handled successfully!")
                    st.session_state.processed_df = df_copy
        
        # Encode Categorical Variables
        with sub_tab2:
            st.subheader("Encode Categorical Variables")
            
            encoding_option = st.selectbox(
                "Select encoding method:",
                ["None", "Label Encoding", "One-Hot Encoding"],
                key="encoding_option"
            )
            
            if encoding_option != "None":
                cat_cols = st.multiselect(
                    "Select categorical columns:",
                    df_copy.select_dtypes(include=['object', 'category']).columns,
                    key="cat_cols"
                )
            
            if st.button("Apply Encoding", key="apply_encoding"):
                with st.spinner("Encoding categorical variables..."):
                    df_copy = encode_data(
                        df_copy, 
                        encoding_option, 
                        cat_cols if 'cat_cols' in locals() else []
                    )
                    st.success("✅ Encoding applied successfully!")
                    st.session_state.processed_df = df_copy
        
        with sub_tab3:
            st.subheader("Handle Outliers")
            
            num_cols = df_copy.select_dtypes(include=np.number).columns
            selected_cols_outliers = st.multiselect(
                "Select numerical columns to handle outliers:",
                num_cols,
                key="outlier_cols"
            )

            outlier_detect_method = st.radio(
                "Choose outlier detection method:",
                ["IQR", "Z-Score"],
                horizontal=True,
                key="outlier_detect_method"
            )
            if outlier_detect_method == "Z-Score":
                z_threshold_detect = st.slider("Z-Score Threshold for Detection:", 1.0, 5.0, 3.0, step=0.1, key="zscore_detect_threshold")

            if st.button("🔍 Detect Outliers", key="detect_outliers"):
                outlier_info = {}
                for col in selected_cols_outliers if selected_cols_outliers else num_cols:
                    col_data = df_copy[col].dropna()
                    if outlier_detect_method == "IQR":
                        Q1 = col_data.quantile(0.25)
                        Q3 = col_data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                    else:  # Z-Score
                        from scipy.stats import zscore
                        z_scores = zscore(col_data)
                        outliers = col_data[np.abs(z_scores) > z_threshold_detect]
                    outlier_count = outliers.count()
                    if outlier_count > 0:
                        outlier_info[col] = outlier_count
                if outlier_info:
                    st.info(f"Columns with outliers (using {outlier_detect_method} method):")
                    st.dataframe(
                        pd.DataFrame(
                            list(outlier_info.items()), 
                            columns=["Column", "Number of Outliers"]
                        )
                    )
                else:
                    st.success(f"No outliers detected in the selected columns ({outlier_detect_method} method).")

            outlier_method = st.selectbox(
                "Select method to handle outliers:",
                ["None", "Winsorization", "Clipping", "Z-Score", "IQR"],
                key="outlier_method"
            )
            
            if outlier_method == "Winsorization":
                lower_limit = st.slider("Lower limit (e.g., 0.05):", 0.0, 0.5, 0.05, step=0.01, key="winsorize_lower")
                upper_limit = st.slider("Upper limit (e.g., 0.95):", 0.5, 1.0, 0.95, step=0.01, key="winsorize_upper")
            
            elif outlier_method == "Clipping":
                clip_lower = st.number_input("Lower bound for Clipping:", value=0.0, key="clip_lower")
                clip_upper = st.number_input("Upper bound for Clipping:", value=1.0, key="clip_upper")
            
            elif outlier_method == "Z-Score":
                z_threshold = st.slider("Z-Score Threshold (default: 3):", 1.0, 5.0, 3.0, step=0.1, key="z_threshold")
            
            elif outlier_method == "IQR":
                iqr_multiplier = st.slider("IQR Multiplier (default: 1.5):", 1.0, 3.0, 1.5, step=0.1, key="iqr_multiplier")
            
            if st.button("Apply Outlier Handling", key="apply_outliers"):
                with st.spinner("Handling outliers..."):
                    if outlier_method == "Winsorization":
                        for col in selected_cols_outliers:
                            df_copy[col] = winsorize(df_copy[col], limits=(lower_limit, 1 - upper_limit))
                    
                    elif outlier_method == "Clipping":
                        for col in selected_cols_outliers:
                            df_copy[col] = df_copy[col].clip(lower=clip_lower, upper=clip_upper)
                    
                    elif outlier_method == "Z-Score":
                        from scipy.stats import zscore
                        for col in selected_cols_outliers:
                            z_scores = zscore(df_copy[col])
                            df_copy = df_copy[(np.abs(z_scores) <= z_threshold)]
                    
                    elif outlier_method == "IQR":
                        for col in selected_cols_outliers:
                            Q1 = df_copy[col].quantile(0.25)
                            Q3 = df_copy[col].quantile(0.75)
                            IQR = Q3 - Q1
                            lower_bound = Q1 - iqr_multiplier * IQR
                            upper_bound = Q3 + iqr_multiplier * IQR
                            df_copy = df_copy[(df_copy[col] >= lower_bound) & (df_copy[col] <= upper_bound)]
                    
                    st.success("✅ Outliers handled successfully!")
                    st.session_state.processed_df = df_copy

        with sub_tab4:
            st.subheader("Feature Scaling and Transformation")
            
            scaling_option = st.selectbox(
                "Select scaling or transformation method:",
                ["None", "Standard Scaler", "MinMax Scaler", "Log Transformation", "Power Transformation", "Polynomial Transformation"],
                key="scaling_option"
            )
            
            if scaling_option != "None":
                num_cols = st.multiselect(
                    "Select numerical columns:",
                    df_copy.select_dtypes(include=np.number).columns,
                    key="num_cols"
                )
            
            if scaling_option == "Polynomial Transformation":
                degree = st.slider("Select degree for Polynomial Transformation:", 2, 5, 2, key="poly_degree")
            
            if scaling_option == "Power Transformation":
                power_method = st.radio(
                    "Select Power Transformation Method:",
                    ["Box-Cox Transformation", "Yeo-Johnson Transformation"],
                    key="power_method"
                )
            
            if st.button("Apply Scaling or Transformation", key="apply_scaling"):
                with st.spinner("Applying scaling or transformation..."):
                    try:
                        if scaling_option == "Standard Scaler":
                            scaler = StandardScaler()
                            df_copy[num_cols] = scaler.fit_transform(df_copy[num_cols])
                        elif scaling_option == "MinMax Scaler":
                            scaler = MinMaxScaler()
                            df_copy[num_cols] = scaler.fit_transform(df_copy[num_cols])
                        elif scaling_option == "Log Transformation":
                            df_copy[num_cols] = np.log1p(df_copy[num_cols])
                        elif scaling_option == "Power Transformation":
                            scaler = PowerTransformer(method="box-cox" if power_method == "Box-Cox Transformation" else "yeo-johnson")
                            df_copy[num_cols] = scaler.fit_transform(df_copy[num_cols])
                        elif scaling_option == "Polynomial Transformation":
                            from sklearn.preprocessing import PolynomialFeatures
                            poly = PolynomialFeatures(degree=degree, include_bias=False)
                            transformed = poly.fit_transform(df_copy[num_cols])
                            df_copy = pd.DataFrame(transformed, columns=poly.get_feature_names_out(num_cols))
                        
                        st.success("✅ Scaling or transformation applied successfully!")
                        st.session_state.processed_df = df_copy
                    except ValueError as ve:
                        st.error(f"Error: {ve}. Box-Cox Transformation requires all values to be positive.")
                    except Exception as e:
                        st.error(f"Error during scaling or transformation: {str(e)}")
        
        # Feature Selection (PCA)
        with sub_tab5:
            st.subheader("Feature Selection (PCA / RFE)")

            selection_method = st.selectbox(
                "Select Feature Selection Method:",
                ["None", "PCA", "RFE"],
                key="feature_selection_method"
            )

            num_cols = df_copy.select_dtypes(include=np.number).columns.tolist()
            selected_cols = st.multiselect("Select Numerical Columns:", num_cols, key="feature_selection_cols")

            if selection_method == "PCA":
                if selected_cols:
                    if len(selected_cols) < 2:
                        st.warning("PCA requires at least two numerical columns. Only one column selected.")
                        n_components = 1
                    else:
                        n_components = st.slider(
                            "Number of Principal Components:",
                            min_value=1,
                            max_value=len(selected_cols),
                            value=min(2, len(selected_cols)),
                            step=1,
                            key="pca_n_components"
                        )
                    if st.button("Apply PCA", key="apply_pca"):
                        with st.spinner("Applying PCA..."):
                            try:
                                scaler = StandardScaler()
                                scaled_data = scaler.fit_transform(df_copy[selected_cols])
                                pca = PCA(n_components=n_components)
                                pca_transformed = pca.fit_transform(scaled_data)
                                pca_columns = [f"PC{i+1}" for i in range(n_components)]
                                df_pca = pd.DataFrame(pca_transformed, columns=pca_columns, index=df_copy.index)
                                df_copy = pd.concat([df_copy.drop(columns=selected_cols), df_pca], axis=1)
                                st.session_state.processed_df = df_copy
                                st.success(f"✅ PCA applied successfully! Reduced to {n_components} components.")
                                st.dataframe(df_copy.head())

                                # رسم PCA فقط لو كان n_components >= 2
                                if n_components >= 2:
                                    fig = plt.figure(figsize=(8, 6))
                                    plt.scatter(df_pca['PC1'], df_pca['PC2'], color='blue', s=50)
                                    plt.xlabel('Principal Component 1')
                                    plt.ylabel('Principal Component 2')
                                    plt.title(f'PCA: {len(selected_cols)} Features Reduced to 2')
                                    plt.grid(True)
                                    st.pyplot(fig)
                            except ValueError as ve:
                                st.error(f"Error: {ve}. Ensure the selected columns are valid for PCA.")
                            except Exception as e:
                                st.error(f"Unexpected error during PCA: {str(e)}")
                else:
                    st.warning("Please select at least one numerical column for PCA.")

            elif selection_method == "RFE":
                st.info("RFE (Recursive Feature Elimination) requires a supervised model and target variable.")
                if selected_cols:
                    target_cols = [col for col in df_copy.columns if col not in selected_cols]
                    target_col = st.selectbox("Select Target Column for RFE:", target_cols, key="rfe_target_col")
                    n_features = st.slider("Number of Features to Select:", 1, len(selected_cols), min(3, len(selected_cols)), key="rfe_n_features")
                    model_type = st.selectbox("Select Model for RFE:", ["Logistic Regression", "Random Forest"], key="rfe_model_type")

                    if st.button("Apply RFE", key="apply_rfe"):
                        try:
                            X = df_copy[selected_cols]
                            y = df_copy[target_col]
                            if model_type == "Logistic Regression":
                                estimator = LogisticRegression(max_iter=1000)
                            else:
                                estimator = RandomForestClassifier()
                            rfe = RFE(estimator, n_features_to_select=n_features)
                            X_rfe = rfe.fit_transform(X, y)
                            selected_features = np.array(selected_cols)[rfe.support_]
                            st.success(f"Selected Features: {', '.join(selected_features)}")
                            df_copy = pd.concat([df_copy[selected_features], df_copy[target_col]], axis=1)
                            st.session_state.processed_df = df_copy
                            st.dataframe(df_copy.head())
                        except Exception as e:
                            st.error(f"Error during RFE: {str(e)}")
                else:
                    st.warning("Please select at least one numerical column for RFE.")

        # Preview Processed Data
        st.subheader("Preview Processed Data")
        st.dataframe(st.session_state.processed_df.head())
        
        csv = st.session_state.processed_df.to_csv(index=False).encode('utf-8')
        st.download_button(
            label="📥 Download Processed Data",
            data=csv,
            file_name="processed_data.csv",
            mime="text/csv"
        )

# ====== Visualization Tab ======
with tab3:
    st.header("3. Data Visualization")
    
    if st.session_state.processed_df is None:
        st.warning("Please preprocess your data first in the 'Preprocessing' tab.")
    else:
        st.info("Explore your processed data through visualization")
        
        sns.set_style("whitegrid")
        plt.rcParams.update({
            'font.size': 14, 
            'axes.titlesize': 16, 
            'axes.labelsize': 14, 
            'axes.titlepad': 20, 
            'axes.labelpad': 10
        })
        
        plot_type = st.selectbox(
            "Select plot type:",
            ["None", "Pie Chart", "Bar Chart", "Horizontal Bar Chart", "Strip Plot", 
             "Count Plot", "Pair Plot", "Histogram Plot", "Box Plot", "Correlation Heatmap", 
             "KDE Plot", "Scatter Plot", "Violin Plot"],
            key="plot_type"
        )
        
        def get_categorical_columns(df):
            return df.select_dtypes(include=['object', 'category']).columns.tolist()
        
        def get_numeric_columns(df):
            return df.select_dtypes(include=['number']).columns.tolist()
        
        if plot_type == "Pie Chart":
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            if cat_cols:
                selected_col = st.selectbox("Select column:", cat_cols, key="pie_col")
                top_categories = st.session_state.processed_df[selected_col].value_counts().head(5)
                
                plt.figure(figsize=(10, 10))
                plt.pie(
                    top_categories.values,
                    labels=top_categories.index.tolist(),
                    autopct='%1.1f%%',
                    startangle=90,
                    colors=sns.color_palette("Blues_r", len(top_categories)),
                    explode=[0.1] * len(top_categories),
                    shadow=True,
                    textprops={'fontsize': 14, 'weight': 'bold'},
                    wedgeprops={'edgecolor': 'white', 'linewidth': 1.5}
                )
                plt.title(f"Top 5 Categories in {selected_col}")
                plt.tight_layout()
                st.pyplot(plt)
                plt.clf()
            else:
                st.warning("No categorical columns found for pie chart.")
        
        elif plot_type == "Bar Chart":
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            if cat_cols:
                selected_col = st.selectbox("Select column:", cat_cols, key="bar_col")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                st.session_state.processed_df[selected_col].value_counts().plot(
                    kind="bar", color=sns.color_palette("Blues_d")[2], edgecolor='black', ax=ax
                )
                ax.set_title(f"{selected_col} Frequency")
                ax.set_xlabel(selected_col)
                ax.set_ylabel("Count")
                ax.grid(True, axis='y', linestyle='--', alpha=0.7)
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No categorical columns found for bar chart.")
        
        elif plot_type == "Horizontal Bar Chart":
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            if cat_cols:
                selected_col = st.selectbox("Select column:", cat_cols, key="hbar_col")
                
                value_counts = st.session_state.processed_df[selected_col].value_counts().head(10)
                fig, ax = plt.subplots(figsize=(12, 8))
                ax.barh(value_counts.index, value_counts.values, 
                        color=sns.color_palette("Blues_d")[1], edgecolor='black')
                ax.set_xlabel("Count")
                ax.set_ylabel(selected_col)
                ax.set_title(f"Top 10 {selected_col}")
                ax.grid(True, axis='x', linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No categorical columns found for horizontal bar chart.")
        
        elif plot_type == "Strip Plot":
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if cat_cols and num_cols:
                x_col = st.selectbox("X-axis (categorical):", cat_cols, key="strip_x")
                y_col = st.selectbox("Y-axis (numeric):", num_cols, key="strip_y")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.stripplot(x=x_col, y=y_col, data=st.session_state.processed_df, 
                             jitter=True, ax=ax, color='teal', size=6, alpha=0.8)
                ax.set_title(f"{y_col} vs {x_col}")
                ax.set_xlabel(x_col)
                ax.set_ylabel(y_col)
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("Need at least one categorical and one numeric column for strip plot.")
        
        elif plot_type == "Count Plot":
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            if len(cat_cols) >= 2:
                primary = st.selectbox("Primary column:", cat_cols, key="count_primary")
                hue = st.selectbox("Hue column:", cat_cols, key="count_hue")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.countplot(
                    y=primary, hue=hue, data=st.session_state.processed_df,
                    order=st.session_state.processed_df[primary].value_counts().iloc[:10].index,
                    ax=ax, palette='Blues_d'
                )
                ax.set_title(f"Top 10 {primary} by {hue}")
                ax.set_xlabel("Count")
                ax.set_ylabel(primary)
                ax.grid(True, axis='x', linestyle='--', alpha=0.7)
                plt.legend(title=hue, loc='lower right')
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("Need at least two categorical columns for count plot.")
        
        elif plot_type == "Pair Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            cat_cols = get_categorical_columns(st.session_state.processed_df)
            if len(num_cols) >= 2:
                selected_cols = st.multiselect(
                    "Select numeric columns:", num_cols, default=num_cols[:3], key="pair_cols"
                )
                hue = st.selectbox("Hue column (optional):", ["None"] + cat_cols, key="pair_hue")
                if selected_cols:
                    sns.set_style("ticks")
                    pair_plot = sns.pairplot(
                        st.session_state.processed_df,
                        vars=selected_cols,
                        hue=None if hue == "None" else hue,
                        diag_kind='kde',
                        plot_kws={'color': sns.color_palette("Blues_d")[2], 's': 80, 'alpha': 0.8},
                        diag_kws={'color': 'darkorange', 'fill': True}
                    )
                    pair_plot.fig.suptitle("Pair Plot of Selected Features", y=1.02)
                    plt.tight_layout()
                    st.pyplot(pair_plot)
                    plt.clf()
                else:
                    st.warning("Please select at least 2 numeric columns.")
            else:
                st.warning("Need at least 2 numeric columns for pair plot.")
        
        elif plot_type == "Histogram Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if num_cols:
                selected_col = st.selectbox("Select column:", num_cols, key="hist_col")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.histplot(
                    st.session_state.processed_df[selected_col], 
                    kde=True, color='cornflowerblue', edgecolor='black', bins=30, ax=ax
                )
                ax.set_title(f"Histogram of {selected_col}")
                ax.set_xlabel(selected_col)
                ax.set_ylabel("Count")
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No numeric columns found for histogram plot.")
        
        elif plot_type == "Box Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if num_cols:
                selected_col = st.selectbox("Select column:", num_cols, key="box_col")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.boxplot(
                    x=st.session_state.processed_df[selected_col], color='skyblue', ax=ax,
                    boxprops=dict(edgecolor='black', linewidth=1.5)
                )
                ax.set_title(f"Box Plot of {selected_col}")
                ax.set_xlabel(selected_col)
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No numeric columns found for box plot.")
        
        elif plot_type == "Correlation Heatmap":
            numeric_df = st.session_state.processed_df.select_dtypes(include=['number'])
            if not numeric_df.empty:
                plt.figure(figsize=(14, 12))
                sns.heatmap(
                    numeric_df.corr(), 
                    annot=True, cmap='viridis', fmt='.2f', 
                    linewidths=1, square=True,
                    cbar_kws={'label': 'Correlation Coefficient', 'shrink': 0.8},
                    annot_kws={'size': 12, 'weight': 'bold'}
                )
                plt.title("Correlation Heatmap")
                plt.tight_layout()
                st.pyplot(plt)
                plt.clf()
            else:
                st.warning("No numeric columns found for correlation heatmap.")
        
        elif plot_type == "KDE Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if num_cols:
                selected_col = st.selectbox("Select column:", num_cols, key="kde_col")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.kdeplot(
                    st.session_state.processed_df[selected_col].dropna(), 
                    fill=True, color='purple', linewidth=2, ax=ax
                )
                ax.set_title(f"KDE Plot of {selected_col}")
                ax.set_xlabel(selected_col)
                ax.set_ylabel("Density")
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No numeric columns found for KDE plot.")
        
        elif plot_type == "Scatter Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if len(num_cols) >= 2:
                x_col = st.selectbox("X-axis:", num_cols, key="scatter_x")
                y_col = st.selectbox("Y-axis:", num_cols, key="scatter_y")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.scatterplot(
                    x=x_col, y=y_col, data=st.session_state.processed_df, 
                    color='teal', s=100, alpha=0.8, ax=ax, edgecolor='black'
                )
                ax.set_title(f"{y_col} vs {x_col}")
                ax.set_xlabel(x_col)
                ax.set_ylabel(y_col)
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("Need at least 2 numeric columns for scatter plot.")
        
        elif plot_type == "Violin Plot":
            num_cols = get_numeric_columns(st.session_state.processed_df)
            if num_cols:
                selected_col = st.selectbox("Select column:", num_cols, key="violin_col")
                
                fig, ax = plt.subplots(figsize=(12, 8))
                sns.violinplot(
                    x=st.session_state.processed_df[selected_col], 
                    color='lightgreen', ax=ax,
                    inner='quartile', linewidth=1.5
                )
                ax.set_title(f"Violin Plot of {selected_col}")
                ax.set_xlabel(selected_col)
                ax.grid(True, linestyle='--', alpha=0.7)
                plt.tight_layout()
                st.pyplot(fig)
                plt.clf()
            else:
                st.warning("No numeric columns found for violin plot.")

# ====== Model Training Tab ======
with tab4:
    st.header("4. Model Training")
    
    if st.session_state.processed_df is None:
        st.warning("Please preprocess your data first in the 'Preprocessing' tab.")
    else:
        st.info("Select your learning type and train a model")
        
        # Let Streamlit manage the learning_type state via the widget
        st.selectbox(
            "Choose learning type:",
            ["Supervised Learning", "Unsupervised Learning"],
            key="learning_type"
        )
        
        target_options = ["None"] + list(st.session_state.processed_df.columns) if st.session_state.learning_type == "Unsupervised Learning" else list(st.session_state.processed_df.columns)
        target_col = st.selectbox(
            "Select target variable:",
            target_options,
            key="target_col",
            disabled=st.session_state.learning_type == "Unsupervised Learning"
        )
        
        task_type = detect_task_type(st.session_state.processed_df, target_col if st.session_state.learning_type == "Supervised Learning" else "None")
        st.session_state.task_type = task_type
        st.info(f"Detected Task Type: {task_type}")
        
        models = get_models(task_type, st.session_state.learning_type)
        model_name = st.selectbox("Select Model", list(models.keys()))
        model = models[model_name]
        
        if model_name == "KMeans":
            n_clusters = st.slider(
                "Number of Clusters:",
                min_value=2,
                max_value=10,
                value=3,
                step=1,
                key="kmeans_n_clusters",
                disabled=st.session_state.learning_type != "Unsupervised Learning"
            )
            model = KMeans(n_clusters=n_clusters, random_state=42)
        
        metrics = get_metrics(task_type)
        metric_name = st.selectbox("Select Metric", list(metrics.keys()))
        metric_func = metrics[metric_name]
        
        if st.button("🎯 Train Model", key="train_btn"):
            with st.spinner("Training model..."):
                try:
                    if len(st.session_state.processed_df) < 10:
                        st.error("Too few rows to train the model!")
                    else:
                        if st.session_state.processed_df.isnull().any().any():
                            st.error("Data contains missing values. Please handle them in the 'Preprocessing' tab.")
                        else:
                            (X_train, X_test, y_train, y_test), feature_columns = prepare_data(st.session_state.processed_df, target_col)
                            
                            if st.session_state.learning_type == "Supervised Learning" and task_type == "classification" and y_train is not None and len(np.unique(y_train)) < 2:
                                st.error("Target variable contains only one class, cannot train a classification model!")
                            else:
                                trained_model = train_single_model(model, X_train, y_train if task_type != "clustering" else None)
                                y_pred = trained_model.predict(X_test)
                                score = evaluate_model(trained_model, X_test, y_test if task_type != "clustering" else None, metric_func, task_type)
                                
                                if score is not None:
                                    st.success(f"Model {model_name} achieved {metric_name}: {score:.4f}")
                                    
                                    # Store model and data in session state
                                    st.session_state.model = trained_model
                                    st.session_state.X_train = X_train
                                    st.session_state.X_test = X_test
                                    st.session_state.y_train = y_train
                                    st.session_state.y_test = y_test
                                    st.session_state.y_pred = y_pred
                                    st.session_state.feature_columns = feature_columns
                                    st.session_state.task_type = task_type
                                    
                                    save_model(trained_model, feature_columns)
                                    
                                    model_buffer = io.BytesIO()
                                    pickle.dump(trained_model, model_buffer)
                                    st.download_button(
                                        label="💾 Download Model",
                                        data=model_buffer,
                                        file_name=f"{model_name.replace(' ', '_')}_model.pkl",
                                        mime="application/octet-stream"
                                    )
                except Exception as e:
                    st.error(f"Error in model training: {str(e)}")

# ====== Evaluation Tab ======
with tab5:
    st.header("5. Model Evaluation")
    
    if st.session_state.model is None or st.session_state.X_test is None or st.session_state.y_pred is None:
        st.warning("Please train a model first in the 'Model Training' tab.")
    else:
        st.info("Evaluate your model's performance")
        
        if st.session_state.task_type == "clustering":
            st.subheader("Clustering Metrics")
            silhouette = silhouette_score(st.session_state.X_test, st.session_state.y_pred)
            st.metric("Silhouette Score", f"{silhouette:.4f}")
            
            # Visualize clusters
            if st.session_state.X_test.shape[1] >= 2:
                st.subheader("Cluster Visualization")
                plt.figure(figsize=(10, 8))
                sns.scatterplot(
                    x=st.session_state.X_test.iloc[:, 0], 
                    y=st.session_state.X_test.iloc[:, 1], 
                    hue=st.session_state.y_pred, 
                    palette="deep", 
                    s=100, 
                    alpha=0.8
                )
                plt.title("K-Means Clustering Results")
                plt.xlabel(st.session_state.feature_columns[0])
                plt.ylabel(st.session_state.feature_columns[1])
                plt.tight_layout()
                st.pyplot(plt)
                plt.clf()
            else:
                st.info("Cannot visualize clusters with less than 2 features.")
        else:
            tab_metrics, tab_confusion, tab_report, tab_roc = st.tabs([
                "📊 Metrics", 
                "🧮 Confusion Matrix", 
                "📝 Classification Report", 
                "📈 ROC Curve"
            ])
            
            with tab_metrics:
                st.subheader("Performance Metrics")
                
                col1, col2, col3, col4 = st.columns(4)
                if st.session_state.task_type == "classification":
                    with col1:
                        st.metric("Accuracy", f"{accuracy_score(st.session_state.y_test, st.session_state.y_pred):.4f}")
                    with col2:
                        st.metric("Precision", f"{precision_score(st.session_state.y_test, st.session_state.y_pred, average='weighted'):.4f}")
                    with col3:
                        st.metric("Recall", f"{recall_score(st.session_state.y_test, st.session_state.y_pred, average='weighted'):.4f}")
                    with col4:
                        st.metric("F1 Score", f"{f1_score(st.session_state.y_test, st.session_state.y_pred, average='weighted'):.4f}")
                else:  # regression
                    with col1:
                        st.metric("MSE", f"{mean_squared_error(st.session_state.y_test, st.session_state.y_pred):.4f}")
                    with col2:
                        st.metric("MAE", f"{mean_absolute_error(st.session_state.y_test, st.session_state.y_pred):.4f}")
                    with col3:
                        st.metric("R2", f"{r2_score(st.session_state.y_test, st.session_state.y_pred):.4f}")
            
            with tab_confusion:
                if st.session_state.task_type == "classification":
                    st.subheader("Confusion Matrix")
                    
                    cm = confusion_matrix(st.session_state.y_test, st.session_state.y_pred)
                    plt.figure(figsize=(8, 6))
                    sns.heatmap(
                        cm, 
                        annot=True, 
                        fmt="d", 
                        cmap="Blues",
                        xticklabels=np.unique(st.session_state.y_test),
                        yticklabels=np.unique(st.session_state.y_test)
                    )
                    plt.xlabel("Predicted")
                    plt.ylabel("Actual")
                    plt.title("Confusion Matrix")
                    st.pyplot(plt)
                    plt.clf()
                else:
                    st.info("Confusion Matrix is only available for classification tasks.")
            
            with tab_report:
                if st.session_state.task_type == "classification":
                    st.subheader("Classification Report")
                    
                    report = classification_report(
                        st.session_state.y_test, 
                        st.session_state.y_pred,
                        output_dict=True
                    )
                    report_df = pd.DataFrame(report).transpose()
                    st.dataframe(report_df.style.background_gradient(cmap="Blues"))
                    
                    csv = report_df.to_csv().encode('utf-8')
                    st.download_button(
                        label="📥 Download Report",
                        data=csv,
                        file_name="classification_report.csv",
                        mime="text/csv"
                    )
                else:
                    st.info("Classification Report is only available for classification tasks.")
            
            with tab_roc:
                if st.session_state.task_type == "classification" and hasattr(st.session_state.model, "predict_proba"):
                    st.subheader("ROC Curve")
                    
                    y_probs = st.session_state.model.predict_proba(st.session_state.X_test)
                    if y_probs.shape[1] == 2:  # Binary classification
                        fpr, tpr, _ = roc_curve(st.session_state.y_test, y_probs[:, 1])
                        roc_auc = auc(fpr, tpr)
                        
                        plt.figure(figsize=(8, 6))
                        plt.plot(fpr, tpr, color='darkorange', lw=2, 
                                label=f'ROC curve (area = {roc_auc:.2f})')
                        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                        plt.xlim([0.0, 1.0])
                        plt.ylim([0.0, 1.05])
                        plt.xlabel('False Positive Rate')
                        plt.ylabel('True Positive Rate')
                        plt.title('Receiver Operating Characteristic')
                        plt.legend(loc="lower right")
                        st.pyplot(plt)
                        plt.clf()
                    else:
                        st.info("ROC Curve is currently supported only for binary classification.")
                else:
                    st.info("ROC Curve is only available for classification models with probability estimates.")

# ====== Sidebar ======
with st.sidebar:
    st.title("🔧 Settings")
    st.markdown("---")
    
    st.subheader("Workflow Progress")
    if st.session_state.df is not None:
        st.success("✓ Data Uploaded")
    else:
        st.warning("Data Not Uploaded")
        
    if st.session_state.processed_df is not None:
        st.success("✓ Data Preprocessed")
    else:
        st.warning("Data Not Processed")
        
    if st.session_state.model is not None:
        st.success("✓ Model Trained")
    else:
        st.warning("Model Not Trained")
    
    st.markdown("---")
    st.subheader("App Info")
    st.markdown("""
    Machine Learning Workflow App
    
    A complete tool for:
    - Data preprocessing
    - Visualization
    - Model training
    - Evaluation
    
    Follow the tabs from left to right.
    """)
    
    st.markdown("---")
    st.info("""
    Tip: Complete each step before moving to the next one.
    Check the progress indicators in the sidebar.
    """)
