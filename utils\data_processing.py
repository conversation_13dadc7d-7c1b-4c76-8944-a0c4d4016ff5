"""
Data processing utilities for the laptop recommendation system.
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.neighbors import KNeighborsClassifier
from scipy.stats import zscore

# Handle IterativeImputer import for different sklearn versions
try:
    from sklearn.impute import IterativeImputer
except ImportError:
    IterativeImputer = None

def handle_missing_values(data, strategy='simple_mean'):
    """Handle missing values in the dataset."""
    data = data.copy()

    # أولاً نعالج الأعمدة النصية (Categorical)
    for column in data.select_dtypes(include='object').columns:
        if data[column].isnull().sum() > 0:
            mode_value = data[column].mode()
            if not mode_value.empty:
                data[column] = data[column].fillna(mode_value[0])

    # ثم نعالج الأعمدة الرقمية (Numerical)
    numeric_cols = data.select_dtypes(exclude='object').columns
    if strategy.startswith('simple'):
        if strategy == 'simple_mean':
            imputer = SimpleImputer(strategy='mean')
        elif strategy == 'simple_median':
            imputer = SimpleImputer(strategy='median')
        elif strategy == 'simple_mode':
            imputer = SimpleImputer(strategy='most_frequent')
        else:
            raise ValueError("Invalid simple strategy")
    elif strategy == 'knn':
        imputer = KNNImputer(n_neighbors=3)
    elif strategy == 'iterative':
        if IterativeImputer is None:
            raise ImportError("IterativeImputer is not available in your scikit-learn version.")
        imputer = IterativeImputer(random_state=0)
    else:
        raise ValueError("Unsupported strategy")

    data[numeric_cols] = imputer.fit_transform(data[numeric_cols])
    return data

def detect_outliers(df, columns, method='zscore', threshold=3):
    """Detect outliers in specified columns."""
    outliers = pd.DataFrame()

    for column in columns:
        if column in df.columns:
            if method == 'zscore':
                z_scores = zscore(df[column].dropna())
                abs_z_scores = np.abs(z_scores)
                outlier_mask = abs_z_scores > threshold
                # Align mask with original index
                mask_index = df[column].dropna().index
                column_outliers = df.loc[mask_index[outlier_mask]]
            elif method == 'iqr':
                Q1 = df[column].quantile(0.25)
                Q3 = df[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outlier_mask = (df[column] < lower_bound) | (df[column] > upper_bound)
                column_outliers = df[outlier_mask]
            else:
                continue
            outliers = pd.concat([outliers, column_outliers])
    outliers = outliers.drop_duplicates()
    return outliers

def clean_data(df):
    """Clean the dataset by removing duplicates and unnecessary columns."""
    df_clean = df.copy()

    # Only drop columns if they exist
    columns_to_drop = [col for col in ['CPU', 'GPU'] if col in df_clean.columns]
    if columns_to_drop:
        df_clean = df_clean.drop(columns=columns_to_drop)

    df_clean = df_clean.drop_duplicates()

    feature_columns = [col for col in ['RAM', 'Storage', 'Final Price', 'Touch'] if col in df_clean.columns]
    df_clean = df_clean.drop_duplicates(subset=feature_columns)

    return df_clean

def apply_encoding(data):
    """Apply encoding to categorical variables."""
    data = data.copy()

    # Identify categorical (string) columns
    string_columns = data.select_dtypes(include=['object']).columns.tolist()

    if not string_columns:
        print("No categorical columns found for encoding.")
        return data

    # Automatically select encoding method based on the number of string columns
    if len(string_columns) <= 3:
        print(f"{len(string_columns)} categorical columns detected. Using One-Hot Encoding.")
        data = pd.get_dummies(data, columns=string_columns)
    else:
        print(f"{len(string_columns)} categorical columns detected. Using Label Encoding.")
        label_encoder = LabelEncoder()
        for col in string_columns:
            data[col] = label_encoder.fit_transform(data[col].astype(str))

    return data

def scale_columns(data, cols_to_scale):
    """Scale specified columns using StandardScaler."""
    # Only scale columns that exist in the data
    existing_cols = [col for col in cols_to_scale if col in data.columns]

    if existing_cols:
        scaler = StandardScaler()
        data[existing_cols] = scaler.fit_transform(data[existing_cols])

    return data

def build_recommendation_model(df):
    """Build the recommendation model."""
    # تحديد الأعمدة المهمة للتوصية
    feature_cols = []
    if 'RAM' in df.columns: feature_cols.append('RAM')
    if 'Storage' in df.columns: feature_cols.append('Storage')
    if 'Final Price' in df.columns: feature_cols.append('Final Price')
    if 'Touch' in df.columns: feature_cols.append('Touch')
    if 'Screen' in df.columns: feature_cols.append('Screen')
    if 'Brand' in df.columns: feature_cols.append('Brand')
    if not feature_cols:
        return None, None

    # 1. معالجة القيم المفقودة
    processed_data = handle_missing_values(df)

    # 2. تنظيف البيانات (إزالة التكرارات)
    processed_data = clean_data(processed_data)

    # 3. ترميز الأعمدة النصية
    processed_data = apply_encoding(processed_data)

    # 4. تقييس الأعمدة الرقمية
    numeric_cols = processed_data.select_dtypes(include=['int', 'float']).columns
    processed_data = scale_columns(processed_data, numeric_cols)

    # 5. تحديد الأعمدة المتاحة بعد المعالجة
    available_features = [col for col in feature_cols if col in processed_data.columns]
    if not available_features:
        return None, None

    # 6. تجهيز المحولات للمعالجة المسبقة
    numeric_features = processed_data[available_features].select_dtypes(include=np.number).columns.tolist()
    categorical_features = processed_data[available_features].select_dtypes(exclude=np.number).columns.tolist()

    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())
    ])
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore'))
    ])
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)
        ]
    )

    # 7. بناء نموذج KNN للتوصية
    model = Pipeline(steps=[
        ('preprocessor', preprocessor),
        ('model', KNeighborsClassifier(n_neighbors=5))
    ])

    # 8. تدريب النموذج باستخدام target وهمي (مؤشر الصفوف)
    model.fit(processed_data[available_features], np.arange(len(processed_data)))

    return model, available_features
