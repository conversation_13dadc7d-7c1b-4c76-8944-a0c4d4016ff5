#!/usr/bin/env python3
"""
Test script to validate the Smart Laptop Recommender application
"""

import pandas as pd
import numpy as np
import sys
import os

def test_data_loading():
    """Test if the dataset can be loaded"""
    print("🔍 Testing data loading...")
    
    if not os.path.exists("laptops.csv"):
        print("❌ Error: laptops.csv not found!")
        return False
    
    try:
        data = pd.read_csv("laptops.csv")
        print(f"✅ Data loaded successfully: {data.shape[0]} rows, {data.shape[1]} columns")
        
        # Check required columns
        required_cols = ['Brand', 'Model', 'Final Price', 'RAM', 'Storage']
        missing_cols = [col for col in required_cols if col not in data.columns]
        
        if missing_cols:
            print(f"⚠️  Warning: Missing columns: {missing_cols}")
        else:
            print("✅ All required columns present")
        
        return True
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def test_feature_engineering():
    """Test feature engineering functions"""
    print("\n🔧 Testing feature engineering...")
    
    try:
        # Import the functions from the main app
        sys.path.append('.')
        
        # Create sample data for testing
        sample_data = pd.DataFrame({
            'Brand': ['ASUS', 'HP', 'Lenovo'],
            'Model': ['VivoBook', 'Pavilion', 'ThinkPad'],
            'CPU': ['Intel Core i5', 'AMD Ryzen 7', 'Intel Core i7'],
            'RAM': [8, 16, 16],
            'Storage': [512, 1000, 512],
            'Final Price': [699, 899, 1299],
            'Screen': [15.6, 15.6, 14.0],
            'GPU': ['Integrated', 'RTX 3050', 'Integrated']
        })
        
        # Test CPU performance extraction
        def extract_cpu_performance(cpu_str):
            cpu_str = str(cpu_str).lower()
            if any(x in cpu_str for x in ['i9', 'ryzen 9', 'i7', 'ryzen 7']):
                return 'High Performance'
            elif any(x in cpu_str for x in ['i5', 'ryzen 5']):
                return 'Mid Performance'
            elif any(x in cpu_str for x in ['i3', 'ryzen 3']):
                return 'Basic Performance'
            else:
                return 'Entry Level'
        
        sample_data['CPU_Performance'] = sample_data['CPU'].apply(extract_cpu_performance)
        print("✅ CPU performance categorization working")
        
        # Test usage categorization
        def categorize_usage(row):
            gpu = str(row.get('GPU', '')).lower()
            ram = row.get('RAM', 0)
            cpu_perf = row.get('CPU_Performance', 'Entry Level')
            
            if any(x in gpu for x in ['rtx', 'gtx', 'radeon rx']) and ram >= 16:
                return 'Gaming'
            elif any(x in gpu for x in ['rtx', 'gtx', 'quadro', 'radeon']) and cpu_perf in ['High Performance', 'Mid Performance']:
                return 'Graphics'
            else:
                return 'Studying'
        
        sample_data['Usage_Category'] = sample_data.apply(categorize_usage, axis=1)
        print("✅ Usage categorization working")
        
        return True
    except Exception as e:
        print(f"❌ Error in feature engineering: {e}")
        return False

def test_imports():
    """Test if all required libraries can be imported"""
    print("\n📦 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported")
        
        import pandas as pd
        print("✅ Pandas imported")
        
        import numpy as np
        print("✅ Numpy imported")
        
        import matplotlib.pyplot as plt
        print("✅ Matplotlib imported")
        
        import seaborn as sns
        print("✅ Seaborn imported")
        
        import plotly.express as px
        print("✅ Plotly imported")
        
        from sklearn.ensemble import RandomForestClassifier
        print("✅ Scikit-learn imported")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔥 Smart Laptop Recommender - Application Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_data_loading,
        test_feature_engineering
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("🚀 Run: streamlit run Final_DA_Project.py")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
