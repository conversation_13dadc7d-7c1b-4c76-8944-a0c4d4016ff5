import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from data_processor import LaptopDataProcessor
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

class LaptopDataVisualizer:
    def __init__(self, processor):
        self.processor = processor
        self.df = processor.processed_df
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def create_price_distribution_plots(self):
        """Create price distribution visualizations"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Laptop Price Distribution Analysis', fontsize=16, fontweight='bold')
        
        # Overall price distribution
        axes[0, 0].hist(self.df['Final Price'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Overall Price Distribution')
        axes[0, 0].set_xlabel('Price (€)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].axvline(self.df['Final Price'].mean(), color='red', linestyle='--', 
                          label=f'Mean: €{self.df["Final Price"].mean():.0f}')
        axes[0, 0].legend()
        
        # Price by brand (top 8 brands)
        top_brands = self.df['Brand'].value_counts().head(8).index
        brand_data = self.df[self.df['Brand'].isin(top_brands)]
        sns.boxplot(data=brand_data, x='Brand', y='Final Price', ax=axes[0, 1])
        axes[0, 1].set_title('Price Distribution by Brand (Top 8)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Price by usage category
        sns.violinplot(data=self.df, x='Usage_Category', y='Final Price', ax=axes[1, 0])
        axes[1, 0].set_title('Price Distribution by Usage Category')
        
        # Price by CPU performance
        sns.boxplot(data=self.df, x='CPU_Performance', y='Final Price', ax=axes[1, 1])
        axes[1, 1].set_title('Price Distribution by CPU Performance')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def create_performance_analysis(self):
        """Create performance vs price analysis"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Performance vs Price Analysis', fontsize=16, fontweight='bold')
        
        # Performance score vs price
        scatter = axes[0, 0].scatter(self.df['Performance_Score'], self.df['Final Price'], 
                                   c=self.df['Has_Dedicated_GPU'], cmap='viridis', alpha=0.6)
        axes[0, 0].set_xlabel('Performance Score')
        axes[0, 0].set_ylabel('Price (€)')
        axes[0, 0].set_title('Performance Score vs Price')
        plt.colorbar(scatter, ax=axes[0, 0], label='Has Dedicated GPU')
        
        # RAM vs Price colored by usage
        usage_colors = {'Gaming': 'red', 'Graphics': 'blue', 'Studying': 'green'}
        for usage in self.df['Usage_Category'].unique():
            data = self.df[self.df['Usage_Category'] == usage]
            axes[0, 1].scatter(data['RAM'], data['Final Price'], 
                             label=usage, alpha=0.6, color=usage_colors.get(usage, 'gray'))
        axes[0, 1].set_xlabel('RAM (GB)')
        axes[0, 1].set_ylabel('Price (€)')
        axes[0, 1].set_title('RAM vs Price by Usage Category')
        axes[0, 1].legend()
        
        # Value score distribution
        axes[1, 0].hist(self.df['Value_Score'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[1, 0].set_xlabel('Value Score (Performance per €)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Value Score Distribution')
        axes[1, 0].axvline(self.df['Value_Score'].mean(), color='darkred', linestyle='--', 
                          label=f'Mean: {self.df["Value_Score"].mean():.3f}')
        axes[1, 0].legend()
        
        # Storage vs Price
        axes[1, 1].scatter(self.df['Storage'], self.df['Final Price'], alpha=0.6, color='orange')
        axes[1, 1].set_xlabel('Storage (GB)')
        axes[1, 1].set_ylabel('Price (€)')
        axes[1, 1].set_title('Storage vs Price')
        
        plt.tight_layout()
        return fig
    
    def create_correlation_heatmap(self):
        """Create correlation heatmap of numerical features"""
        numerical_cols = ['RAM', 'Storage', 'Screen', 'Final Price', 'Has_Dedicated_GPU', 
                         'Performance_Score', 'Value_Score', 'CPU_Score']
        
        correlation_matrix = self.df[numerical_cols].corr()
        
        plt.figure(figsize=(10, 8))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('Feature Correlation Heatmap', fontsize=14, fontweight='bold')
        plt.tight_layout()
        return plt.gcf()
    
    def create_usage_category_analysis(self):
        """Analyze usage categories"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Usage Category Analysis', fontsize=16, fontweight='bold')
        
        # Usage category distribution
        usage_counts = self.df['Usage_Category'].value_counts()
        axes[0, 0].pie(usage_counts.values, labels=usage_counts.index, autopct='%1.1f%%', 
                      colors=['#ff9999', '#66b3ff', '#99ff99'])
        axes[0, 0].set_title('Usage Category Distribution')
        
        # Average specs by usage category
        usage_stats = self.df.groupby('Usage_Category')[['RAM', 'Storage', 'Final Price']].mean()
        usage_stats.plot(kind='bar', ax=axes[0, 1])
        axes[0, 1].set_title('Average Specs by Usage Category')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].legend()
        
        # Weight category distribution
        weight_counts = self.df['Weight_Category'].value_counts()
        axes[1, 0].bar(weight_counts.index, weight_counts.values, color=['lightblue', 'lightgreen', 'lightcoral'])
        axes[1, 0].set_title('Weight Category Distribution')
        axes[1, 0].set_ylabel('Count')
        
        # GPU distribution by usage
        gpu_usage = pd.crosstab(self.df['Usage_Category'], self.df['Has_Dedicated_GPU'])
        gpu_usage.plot(kind='bar', stacked=True, ax=axes[1, 1], color=['lightcoral', 'lightblue'])
        axes[1, 1].set_title('GPU Distribution by Usage Category')
        axes[1, 1].set_xlabel('Usage Category')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].legend(['Integrated GPU', 'Dedicated GPU'])
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def create_interactive_plotly_charts(self):
        """Create interactive Plotly charts for the dashboard"""
        charts = {}
        
        # 1. Interactive scatter plot: Performance vs Price
        fig1 = px.scatter(self.df, x='Performance_Score', y='Final Price', 
                         color='Usage_Category', size='RAM',
                         hover_data=['Brand', 'Model', 'CPU', 'GPU'],
                         title='Performance Score vs Price (Interactive)',
                         color_discrete_map={'Gaming': '#ff4444', 'Graphics': '#4444ff', 'Studying': '#44ff44'})
        fig1.update_layout(height=500)
        charts['performance_vs_price'] = fig1
        
        # 2. Brand comparison
        brand_stats = self.df.groupby('Brand').agg({
            'Final Price': 'mean',
            'Performance_Score': 'mean',
            'Value_Score': 'mean'
        }).round(2).reset_index()
        
        fig2 = px.bar(brand_stats.head(10), x='Brand', y='Final Price',
                     title='Average Price by Brand (Top 10)',
                     color='Performance_Score',
                     color_continuous_scale='viridis')
        fig2.update_layout(height=400)
        charts['brand_comparison'] = fig2
        
        # 3. Usage category sunburst
        fig3 = px.sunburst(self.df, path=['Usage_Category', 'Weight_Category', 'Brand'], 
                          values='Final Price',
                          title='Laptop Categories Breakdown')
        fig3.update_layout(height=500)
        charts['category_breakdown'] = fig3
        
        return charts
    
    def save_all_plots(self, output_dir='plots'):
        """Save all plots to files"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # Save matplotlib plots
        fig1 = self.create_price_distribution_plots()
        fig1.savefig(f'{output_dir}/price_distribution.png', dpi=300, bbox_inches='tight')
        plt.close(fig1)
        
        fig2 = self.create_performance_analysis()
        fig2.savefig(f'{output_dir}/performance_analysis.png', dpi=300, bbox_inches='tight')
        plt.close(fig2)
        
        fig3 = self.create_correlation_heatmap()
        fig3.savefig(f'{output_dir}/correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close(fig3)
        
        fig4 = self.create_usage_category_analysis()
        fig4.savefig(f'{output_dir}/usage_analysis.png', dpi=300, bbox_inches='tight')
        plt.close(fig4)
        
        print(f"All plots saved to {output_dir}/ directory")

if __name__ == "__main__":
    # Test the visualizer
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()
    
    visualizer = LaptopDataVisualizer(processor)
    
    # Create and show plots
    print("Creating visualizations...")
    visualizer.create_price_distribution_plots()
    plt.show()
    
    visualizer.create_performance_analysis()
    plt.show()
    
    visualizer.create_correlation_heatmap()
    plt.show()
    
    visualizer.create_usage_category_analysis()
    plt.show()
    
    # Save plots
    visualizer.save_all_plots()
