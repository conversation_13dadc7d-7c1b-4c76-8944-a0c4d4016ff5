"""
Smart Laptop Recommendation System
Complete Data Science Project with ML Pipeline and GUI

This script demonstrates the complete pipeline:
1. Data loading and preprocessing
2. Exploratory data analysis and visualization
3. Machine learning model training and evaluation
4. Recommendation system implementation

Run this script to see the complete analysis, or run streamlit_app.py for the GUI.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from data_processor import LaptopDataProcessor
from data_visualization import LaptopDataVisualizer
from model_evaluation import ModelEvaluator
from recommendation_engine import LaptopRecommendationEngine
import warnings
warnings.filterwarnings('ignore')

def main():
    print("🔥 SMART LAPTOP RECOMMENDATION SYSTEM")
    print("=" * 60)
    print("Complete Data Science Project with ML Pipeline")
    print("=" * 60)
    
    # Step 1: Data Loading and Preprocessing
    print("\n📊 STEP 1: DATA LOADING AND PREPROCESSING")
    print("-" * 50)
    
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()
    
    # Display data summary
    summary = processor.get_data_summary()
    print("\n📈 Dataset Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # Step 2: Data Visualization and EDA
    print("\n📈 STEP 2: EXPLORATORY DATA ANALYSIS")
    print("-" * 50)
    
    visualizer = LaptopDataVisualizer(processor)
    
    print("Creating visualizations...")
    print("  ✓ Price distribution analysis")
    print("  ✓ Performance vs price analysis")
    print("  ✓ Correlation heatmap")
    print("  ✓ Usage category analysis")
    
    # Save plots
    visualizer.save_all_plots()
    print("  ✓ All plots saved to 'plots/' directory")
    
    # Step 3: Machine Learning Model Evaluation
    print("\n🤖 STEP 3: MACHINE LEARNING MODEL EVALUATION")
    print("-" * 50)
    
    evaluator = ModelEvaluator(processor)
    results = evaluator.run_complete_evaluation()
    
    print("\n📊 Model Comparison Summary:")
    comparison_df = results['comparison_df']
    print(comparison_df.to_string(index=False, float_format='%.4f'))
    
    # Step 4: Recommendation System
    print("\n🎯 STEP 4: RECOMMENDATION SYSTEM")
    print("-" * 50)
    
    engine = LaptopRecommendationEngine(processor)
    engine.train_recommendation_model()
    
    # Test different scenarios
    test_scenarios = [
        {
            'name': 'Gaming Enthusiast',
            'preferences': {
                'usage_type': 'Gaming',
                'budget_min': 1000,
                'budget_max': 1800,
                'weight_preference': 'Any'
            }
        },
        {
            'name': 'Budget Student',
            'preferences': {
                'usage_type': 'Studying',
                'budget_min': 300,
                'budget_max': 700,
                'weight_preference': 'Light'
            }
        },
        {
            'name': 'Graphics Professional',
            'preferences': {
                'usage_type': 'Graphics',
                'budget_min': 1200,
                'budget_max': 2500,
                'weight_preference': 'Any'
            }
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🎮 Testing scenario: {scenario['name']}")
        print(f"   Preferences: {scenario['preferences']}")
        
        recommendations, message = engine.get_smart_recommendations(
            scenario['preferences'], top_n=3
        )
        
        print(f"   Result: {message}")
        
        if not recommendations.empty:
            print("   Top 3 Recommendations:")
            for i, (_, laptop) in enumerate(recommendations.head(3).iterrows(), 1):
                print(f"     {i}. {laptop['Brand']} {laptop['Model']}")
                print(f"        Price: €{laptop['Final Price']:.2f}")
                print(f"        Score: {laptop['Recommendation_Score']:.1f}/100")
                print(f"        Reason: {laptop['Recommendation_Reason']}")
        else:
            suggestions = engine.get_alternative_suggestions(scenario['preferences'])
            if suggestions:
                print("   Suggestions:")
                for suggestion in suggestions:
                    print(f"     • {suggestion}")
    
    # Step 5: Feature Importance Analysis
    print("\n🎯 STEP 5: FEATURE IMPORTANCE ANALYSIS")
    print("-" * 50)
    
    importance_df = engine.get_feature_importance()
    print("\nTop 10 Most Important Features:")
    print(importance_df.head(10).to_string(index=False, float_format='%.4f'))
    
    # Final Summary
    print("\n" + "=" * 60)
    print("🎉 PROJECT COMPLETION SUMMARY")
    print("=" * 60)
    
    print("✅ Data Processing:")
    print(f"   • Loaded {len(processor.df)} laptops from dataset")
    print(f"   • Cleaned missing values and engineered {len(processor.processed_df.columns) - len(processor.df.columns)} new features")
    print(f"   • Created usage categories: {list(processor.processed_df['Usage_Category'].unique())}")
    
    print("\n✅ Machine Learning:")
    best_model = comparison_df.loc[comparison_df['K-Fold Mean Accuracy'].idxmax(), 'Model']
    best_accuracy = comparison_df['K-Fold Mean Accuracy'].max()
    print(f"   • Evaluated 4 ML algorithms")
    print(f"   • Best model: {best_model} ({best_accuracy:.4f} accuracy)")
    print(f"   • Compared train-test-split vs K-fold cross-validation")
    
    print("\n✅ Recommendation System:")
    print(f"   • Smart scoring algorithm with multi-criteria evaluation")
    print(f"   • Budget filtering with 10% flexibility")
    print(f"   • Usage-based recommendations with performance scoring")
    
    print("\n✅ Visualizations:")
    print(f"   • 4 comprehensive analysis plots saved")
    print(f"   • Interactive Plotly charts for GUI")
    print(f"   • Correlation analysis and feature importance")
    
    print("\n🚀 Next Steps:")
    print("   • Run 'streamlit run streamlit_app.py' for interactive GUI")
    print("   • Check 'plots/' directory for saved visualizations")
    print("   • Explore different recommendation scenarios")
    
    print("\n💡 GUI Features:")
    print("   • Modern glassmorphism design")
    print("   • Interactive recommendation interface")
    print("   • Real-time data analysis dashboard")
    print("   • Model evaluation metrics")
    print("   • Purchase links and detailed explanations")

if __name__ == "__main__":
    main()
