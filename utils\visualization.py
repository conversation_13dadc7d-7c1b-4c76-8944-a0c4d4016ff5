"""
Visualization utilities for the laptop recommendation system.
"""
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import streamlit as st

def setup_plot_style():
    """Set up consistent plot styling."""
    plt.style.use('default')
    return {
        'colors': ['#32127A', '#008292', '#B3446C', '#E3CCDC', '#CDE0E1', '#F0EFED', '#667eea', '#764ba2'],
        'background': '#F0EFED',
        'text_color': '#32127A'
    }

@st.cache_data
def create_brand_donut_chart(data):
    """Create a donut chart for brand distribution."""
    if 'Brand' not in data.columns:
        return None
    
    style = setup_plot_style()
    top_brands = data['Brand'].value_counts().head(8)
    
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    wedges, texts, autotexts = ax.pie(
        top_brands.values,
        labels=top_brands.index,
        autopct='%1.1f%%',
        colors=style['colors'],
        startangle=90,
        pctdistance=0.85,
        textprops={'fontsize': 11, 'weight': 'bold', 'color': style['text_color']}
    )
    
    # Improve text contrast
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(10)
    
    for text in texts:
        text.set_color(style['text_color'])
        text.set_fontweight('bold')
        text.set_fontsize(10)
    
    # Create donut effect
    centre_circle = plt.Circle((0,0), 0.70, fc=style['background'])
    fig.gca().add_artist(centre_circle)
    
    ax.set_title("Top 8 Brands Market Share", fontsize=16, weight='bold', color=style['text_color'], pad=20)
    plt.tight_layout()
    
    return fig

@st.cache_data
def create_price_by_brand_chart(data):
    """Create a horizontal bar chart for average price by brand."""
    if not all(col in data.columns for col in ['Brand', 'Final Price']):
        return None
    
    style = setup_plot_style()
    brand_price = data.groupby('Brand')['Final Price'].agg(['mean', 'count']).reset_index()
    brand_price = brand_price[brand_price['count'] >= 5].sort_values('mean', ascending=True)
    
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    bars = ax.barh(brand_price['Brand'], brand_price['mean'],
                  color='#008292', alpha=0.8, edgecolor='#32127A', linewidth=1.5)
    
    ax.set_xlabel('Average Price ($)', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_title('Average Price by Brand\n(Brands with 5+ models)', fontsize=14, weight='bold', color=style['text_color'])
    ax.grid(axis='x', alpha=0.3, color=style['text_color'])
    
    # Style the axes
    ax.tick_params(colors=style['text_color'], labelsize=10)
    ax.spines['bottom'].set_color(style['text_color'])
    ax.spines['left'].set_color(style['text_color'])
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # Add value labels
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 20, bar.get_y() + bar.get_height()/2,
               f'${width:.0f}', ha='left', va='center', fontweight='bold', color=style['text_color'])
    
    plt.tight_layout()
    return fig

@st.cache_data
def create_price_distribution_chart(data):
    """Create a histogram for price distribution."""
    if 'Final Price' not in data.columns:
        return None
    
    style = setup_plot_style()
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    n, bins, patches = ax.hist(data['Final Price'], bins=30, alpha=0.8, color='#008292', edgecolor='#32127A')
    
    ax.set_xlabel('Price ($)', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_title('Price Distribution', fontsize=14, weight='bold', color=style['text_color'])
    ax.grid(True, alpha=0.3, color=style['text_color'])
    ax.tick_params(colors=style['text_color'])
    
    for spine in ax.spines.values():
        spine.set_color(style['text_color'])
    
    plt.tight_layout()
    return fig

@st.cache_data
def create_ram_vs_price_scatter(data):
    """Create a scatter plot for RAM vs Price correlation."""
    if not all(col in data.columns for col in ['RAM', 'Final Price']):
        return None
    
    style = setup_plot_style()
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    scatter = ax.scatter(data['RAM'], data['Final Price'],
                       c=data['Final Price'], cmap='viridis',
                       alpha=0.6, s=60, edgecolors='#32127A', linewidth=0.5)
    
    ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_ylabel('Price ($)', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_title('RAM vs Price Correlation', fontsize=14, weight='bold', color=style['text_color'])
    ax.grid(True, alpha=0.3, color=style['text_color'])
    ax.tick_params(colors=style['text_color'])
    
    for spine in ax.spines.values():
        spine.set_color(style['text_color'])
    
    plt.colorbar(scatter, label='Price ($)')
    plt.tight_layout()
    return fig

@st.cache_data
def create_ram_distribution_chart(data):
    """Create a bar chart for RAM distribution."""
    if 'RAM' not in data.columns:
        return None
    
    style = setup_plot_style()
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    ram_counts = data['RAM'].value_counts().sort_index()
    bars = ax.bar(ram_counts.index, ram_counts.values, color='#008292', alpha=0.8, edgecolor='#32127A')
    
    ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_title('RAM Distribution', fontsize=14, weight='bold', color=style['text_color'])
    ax.grid(True, alpha=0.3, color=style['text_color'])
    ax.tick_params(colors=style['text_color'])
    
    # Add value labels on bars
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 5,
               f'{int(height)}', ha='center', va='bottom', fontweight='bold', color=style['text_color'])
    
    for spine in ax.spines.values():
        spine.set_color(style['text_color'])
    
    plt.tight_layout()
    return fig

@st.cache_data
def create_storage_distribution_chart(data):
    """Create a bar chart for storage distribution."""
    if 'Storage' not in data.columns:
        return None
    
    style = setup_plot_style()
    fig, ax = plt.subplots(figsize=(10, 8))
    fig.patch.set_facecolor(style['background'])
    
    # Create storage bins
    storage_bins = [0, 256, 512, 1000, 2000, float('inf')]
    storage_labels = ['≤256GB', '257-512GB', '513GB-1TB', '1-2TB', '>2TB']
    data_copy = data.copy()
    data_copy['storage_category'] = pd.cut(data_copy['Storage'], bins=storage_bins, labels=storage_labels, right=False)
    
    storage_counts = data_copy['storage_category'].value_counts()
    colors = ['#CDE0E1', '#008292', '#B3446C', '#32127A', '#E3CCDC']
    
    bars = ax.bar(range(len(storage_counts)), storage_counts.values,
                 color=colors[:len(storage_counts)], alpha=0.8, edgecolor='#32127A')
    
    ax.set_xlabel('Storage Category', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color=style['text_color'])
    ax.set_title('Storage Distribution', fontsize=14, weight='bold', color=style['text_color'])
    ax.set_xticks(range(len(storage_counts)))
    ax.set_xticklabels(storage_counts.index, rotation=45)
    ax.grid(True, alpha=0.3, color=style['text_color'])
    ax.tick_params(colors=style['text_color'])
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 5,
               f'{int(height)}', ha='center', va='bottom', fontweight='bold', color=style['text_color'])
    
    for spine in ax.spines.values():
        spine.set_color(style['text_color'])
    
    plt.tight_layout()
    return fig
