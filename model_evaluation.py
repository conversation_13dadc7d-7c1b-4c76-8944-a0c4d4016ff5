import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from data_processor import LaptopDataProcessor
import warnings
warnings.filterwarnings('ignore')

class ModelEvaluator:
    def __init__(self, processor):
        self.processor = processor
        self.X, self.y, self.df = processor.prepare_for_ml()
        self.models = {}
        self.results = {}
        self.scaler = StandardScaler()
        
    def initialize_models(self):
        """Initialize different ML models"""
        self.models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        print("Models initialized:", list(self.models.keys()))
    
    def train_test_split_evaluation(self, test_size=0.2):
        """Evaluate models using train-test split"""
        print("\n" + "="*50)
        print("TRAIN-TEST SPLIT EVALUATION")
        print("="*50)
        
        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=42, stratify=self.y
        )
        
        # Scale features for SVM and Logistic Regression
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        split_results = {}
        
        for name, model in self.models.items():
            print(f"\nTraining {name}...")
            
            # Use scaled data for SVM and Logistic Regression
            if name in ['SVM', 'Logistic Regression']:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            split_results[name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'y_test': y_test,
                'y_pred': y_pred
            }
            
            print(f"{name} Results:")
            print(f"  Accuracy:  {accuracy:.4f}")
            print(f"  Precision: {precision:.4f}")
            print(f"  Recall:    {recall:.4f}")
            print(f"  F1-Score:  {f1:.4f}")
        
        self.results['train_test_split'] = split_results
        return split_results
    
    def k_fold_evaluation(self, k=5):
        """Evaluate models using K-fold cross-validation"""
        print("\n" + "="*50)
        print(f"{k}-FOLD CROSS-VALIDATION EVALUATION")
        print("="*50)
        
        kfold = StratifiedKFold(n_splits=k, shuffle=True, random_state=42)
        kfold_results = {}
        
        for name, model in self.models.items():
            print(f"\nEvaluating {name} with {k}-fold CV...")
            
            # Use scaled data for SVM and Logistic Regression
            if name in ['SVM', 'Logistic Regression']:
                X_scaled = self.scaler.fit_transform(self.X)
                scores = cross_val_score(model, X_scaled, self.y, cv=kfold, scoring='accuracy')
            else:
                scores = cross_val_score(model, self.X, self.y, cv=kfold, scoring='accuracy')
            
            kfold_results[name] = {
                'scores': scores,
                'mean_accuracy': scores.mean(),
                'std_accuracy': scores.std()
            }
            
            print(f"{name} Results:")
            print(f"  CV Scores: {scores}")
            print(f"  Mean Accuracy: {scores.mean():.4f} (+/- {scores.std() * 2:.4f})")
        
        self.results['k_fold'] = kfold_results
        return kfold_results
    
    def compare_methods(self):
        """Compare train-test split vs K-fold results"""
        print("\n" + "="*60)
        print("COMPARISON: TRAIN-TEST SPLIT vs K-FOLD CROSS-VALIDATION")
        print("="*60)
        
        comparison_data = []
        
        for model_name in self.models.keys():
            # Train-test split results
            tts_acc = self.results['train_test_split'][model_name]['accuracy']
            
            # K-fold results
            kf_mean = self.results['k_fold'][model_name]['mean_accuracy']
            kf_std = self.results['k_fold'][model_name]['std_accuracy']
            
            comparison_data.append({
                'Model': model_name,
                'Train-Test Split Accuracy': tts_acc,
                'K-Fold Mean Accuracy': kf_mean,
                'K-Fold Std': kf_std,
                'Difference': abs(tts_acc - kf_mean)
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False, float_format='%.4f'))
        
        # Determine best model
        best_model_tts = max(self.results['train_test_split'].items(), 
                           key=lambda x: x[1]['accuracy'])[0]
        best_model_kf = max(self.results['k_fold'].items(), 
                          key=lambda x: x[1]['mean_accuracy'])[0]
        
        print(f"\nBest Model (Train-Test Split): {best_model_tts}")
        print(f"Best Model (K-Fold CV): {best_model_kf}")
        
        return comparison_df
    
    def create_evaluation_plots(self):
        """Create visualization plots for model evaluation"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Model Evaluation Results', fontsize=16, fontweight='bold')
        
        # 1. Train-Test Split Results
        tts_data = []
        for model, results in self.results['train_test_split'].items():
            tts_data.append({
                'Model': model,
                'Accuracy': results['accuracy'],
                'Precision': results['precision'],
                'Recall': results['recall'],
                'F1-Score': results['f1_score']
            })
        
        tts_df = pd.DataFrame(tts_data)
        tts_df.set_index('Model')[['Accuracy', 'Precision', 'Recall', 'F1-Score']].plot(
            kind='bar', ax=axes[0, 0], width=0.8)
        axes[0, 0].set_title('Train-Test Split Results')
        axes[0, 0].set_ylabel('Score')
        axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. K-Fold Cross-Validation Results
        kf_models = list(self.results['k_fold'].keys())
        kf_means = [self.results['k_fold'][model]['mean_accuracy'] for model in kf_models]
        kf_stds = [self.results['k_fold'][model]['std_accuracy'] for model in kf_models]
        
        axes[0, 1].bar(kf_models, kf_means, yerr=kf_stds, capsize=5, alpha=0.7, color='skyblue')
        axes[0, 1].set_title('K-Fold Cross-Validation Results')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Method Comparison
        comparison_data = []
        for model in kf_models:
            comparison_data.append({
                'Model': model,
                'Train-Test Split': self.results['train_test_split'][model]['accuracy'],
                'K-Fold CV': self.results['k_fold'][model]['mean_accuracy']
            })
        
        comp_df = pd.DataFrame(comparison_data)
        comp_df.set_index('Model')[['Train-Test Split', 'K-Fold CV']].plot(
            kind='bar', ax=axes[1, 0], width=0.8)
        axes[1, 0].set_title('Method Comparison')
        axes[1, 0].set_ylabel('Accuracy')
        axes[1, 0].legend()
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. Confusion Matrix for Best Model
        best_model = max(self.results['train_test_split'].items(), 
                        key=lambda x: x[1]['accuracy'])[0]
        y_test = self.results['train_test_split'][best_model]['y_test']
        y_pred = self.results['train_test_split'][best_model]['y_pred']
        
        cm = confusion_matrix(y_test, y_pred)
        labels = self.processor.label_encoders['Usage_Category'].classes_
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=labels, yticklabels=labels, ax=axes[1, 1])
        axes[1, 1].set_title(f'Confusion Matrix - {best_model}')
        axes[1, 1].set_xlabel('Predicted')
        axes[1, 1].set_ylabel('Actual')
        
        plt.tight_layout()
        return fig
    
    def get_detailed_classification_report(self):
        """Get detailed classification report for the best model"""
        best_model = max(self.results['train_test_split'].items(), 
                        key=lambda x: x[1]['accuracy'])[0]
        
        y_test = self.results['train_test_split'][best_model]['y_test']
        y_pred = self.results['train_test_split'][best_model]['y_pred']
        
        labels = self.processor.label_encoders['Usage_Category'].classes_
        target_names = [labels[i] for i in sorted(set(y_test))]
        
        print(f"\nDetailed Classification Report - {best_model}")
        print("="*50)
        print(classification_report(y_test, y_pred, target_names=target_names))
        
        return classification_report(y_test, y_pred, target_names=target_names, output_dict=True)
    
    def run_complete_evaluation(self):
        """Run the complete evaluation pipeline"""
        print("Starting Complete Model Evaluation Pipeline...")
        
        # Initialize models
        self.initialize_models()
        
        # Run evaluations
        self.train_test_split_evaluation()
        self.k_fold_evaluation()
        
        # Compare methods
        comparison_df = self.compare_methods()
        
        # Get detailed report
        detailed_report = self.get_detailed_classification_report()
        
        # Create plots
        fig = self.create_evaluation_plots()
        
        return {
            'comparison_df': comparison_df,
            'detailed_report': detailed_report,
            'evaluation_plot': fig,
            'results': self.results
        }

if __name__ == "__main__":
    # Test the evaluator
    print("Loading and processing data...")
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()
    
    print("Starting model evaluation...")
    evaluator = ModelEvaluator(processor)
    results = evaluator.run_complete_evaluation()
    
    plt.show()
    
    print("\nEvaluation complete! Check the plots and results above.")
