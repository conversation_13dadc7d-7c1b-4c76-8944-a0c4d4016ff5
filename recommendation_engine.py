import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import pickle
import os

class LaptopRecommendationEngine:
    def __init__(self, processor):
        self.processor = processor
        self.df = processor.processed_df
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False

    def train_recommendation_model(self):
        """Train the recommendation model"""
        print("Training recommendation model...")

        # Prepare data for ML
        X, y, _ = self.processor.prepare_for_ml()

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # Train Random Forest (best performing model)
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)

        # Calculate accuracy
        accuracy = self.model.score(X_test, y_test)
        print(f"Model trained with accuracy: {accuracy:.4f}")

        self.is_trained = True
        return accuracy

    def calculate_recommendation_score(self, laptop_row, user_preferences):
        """Calculate recommendation score for a laptop based on user preferences"""
        score = 0
        max_score = 100

        # Budget score (40% weight)
        budget_min = user_preferences['budget_min']
        budget_max = user_preferences['budget_max']
        price = laptop_row['Final Price']

        if budget_min <= price <= budget_max:
            # Perfect budget match
            budget_score = 40
        elif price < budget_min:
            # Under budget - good value
            budget_score = 35 + (budget_min - price) / budget_min * 5
        else:
            # Over budget - penalize
            over_budget_ratio = (price - budget_max) / budget_max
            budget_score = max(0, 40 - over_budget_ratio * 40)

        score += budget_score

        # Usage match score (30% weight)
        usage_preference = user_preferences['usage_type']
        laptop_usage = laptop_row['Usage_Category']

        if usage_preference == 'Any' or usage_preference == laptop_usage:
            usage_score = 30
        elif (usage_preference == 'Gaming' and laptop_usage == 'Graphics') or \
             (usage_preference == 'Graphics' and laptop_usage == 'Gaming'):
            # Gaming and Graphics are somewhat compatible
            usage_score = 25
        else:
            usage_score = 10  # Still usable but not optimal

        score += usage_score

        # Weight preference score (15% weight)
        weight_preference = user_preferences['weight_preference']
        laptop_weight = laptop_row['Weight_Category']

        if weight_preference == 'Any' or weight_preference == laptop_weight:
            weight_score = 15
        else:
            weight_score = 8  # Partial match

        score += weight_score

        # Performance value score (15% weight)
        value_score = laptop_row['Value_Score']
        # Normalize value score to 0-15 range
        max_value = self.df['Value_Score'].max()
        normalized_value = (value_score / max_value) * 15
        score += normalized_value

        return min(score, max_score)

    def get_smart_recommendations(self, user_preferences, top_n=5):
        """Get smart recommendations using ML and scoring algorithm"""

        # Filter by hard constraints first
        filtered_df = self.df.copy()

        # Budget filter (allow 10% flexibility)
        budget_min = user_preferences['budget_min'] * 0.9
        budget_max = user_preferences['budget_max'] * 1.1
        filtered_df = filtered_df[
            (filtered_df['Final Price'] >= budget_min) &
            (filtered_df['Final Price'] <= budget_max)
        ]

        if filtered_df.empty:
            return pd.DataFrame(), "No laptops found in the specified budget range."

        # Calculate recommendation scores
        filtered_df = filtered_df.copy()
        filtered_df['Recommendation_Score'] = filtered_df.apply(
            lambda row: self.calculate_recommendation_score(row, user_preferences),
            axis=1
        )

        # Sort by recommendation score
        recommendations = filtered_df.nlargest(top_n, 'Recommendation_Score')

        # Prepare output with additional info
        output_columns = [
            'Laptop', 'Brand', 'Model', 'CPU', 'RAM', 'Storage', 'GPU',
            'Screen', 'Final Price', 'Usage_Category', 'Weight_Category',
            'Performance_Score', 'Value_Score', 'Recommendation_Score', 'Has_Dedicated_GPU'
        ]

        result = recommendations[output_columns].copy()

        # Add purchase links (mock URLs)
        result['Purchase_Link'] = result.apply(
            lambda row: f"https://shop.example.com/laptop/{row['Brand'].lower()}-{row['Model'].lower().replace(' ', '-')}",
            axis=1
        )

        # Add recommendation reasons
        result['Recommendation_Reason'] = result.apply(
            lambda row: self.generate_recommendation_reason(row, user_preferences),
            axis=1
        )

        return result, "Recommendations generated successfully!"

    def generate_recommendation_reason(self, laptop_row, user_preferences):
        """Generate explanation for why this laptop is recommended"""
        reasons = []

        # Budget reason
        price = laptop_row['Final Price']
        budget_max = user_preferences['budget_max']
        if price <= budget_max * 0.9:
            reasons.append("Great value within budget")
        elif price <= budget_max:
            reasons.append("Perfect budget match")

        # Usage reason
        usage_pref = user_preferences['usage_type']
        laptop_usage = laptop_row['Usage_Category']
        if usage_pref == laptop_usage:
            reasons.append(f"Optimized for {usage_pref.lower()}")

        # Performance reason
        perf_score = laptop_row['Performance_Score']
        if perf_score > self.df['Performance_Score'].quantile(0.8):
            reasons.append("High performance")
        elif perf_score > self.df['Performance_Score'].quantile(0.6):
            reasons.append("Good performance")

        # Value reason
        value_score = laptop_row['Value_Score']
        if value_score > self.df['Value_Score'].quantile(0.8):
            reasons.append("Excellent value for money")

        # GPU reason
        if laptop_row['Has_Dedicated_GPU'] and usage_pref in ['Gaming', 'Graphics']:
            reasons.append("Dedicated graphics card")

        return "; ".join(reasons) if reasons else "Good overall specifications"

    def get_alternative_suggestions(self, user_preferences):
        """Get alternative suggestions if no good matches found"""
        suggestions = []

        # Suggest budget adjustments
        budget_min = user_preferences['budget_min']
        budget_max = user_preferences['budget_max']

        # Check if increasing budget would help
        higher_budget_count = len(self.df[
            (self.df['Final Price'] > budget_max) &
            (self.df['Final Price'] <= budget_max * 1.3)
        ])

        if higher_budget_count > 0:
            suggestions.append(f"Consider increasing budget to €{budget_max * 1.2:.0f} for {higher_budget_count} more options")

        # Check if lowering budget would help
        lower_budget_count = len(self.df[
            (self.df['Final Price'] < budget_min) &
            (self.df['Final Price'] >= budget_min * 0.7)
        ])

        if lower_budget_count > 0:
            suggestions.append(f"Consider lowering budget to €{budget_min * 0.8:.0f} for {lower_budget_count} more options")

        # Usage suggestions
        usage_type = user_preferences['usage_type']
        if usage_type == 'Gaming':
            gaming_count = len(self.df[self.df['Usage_Category'] == 'Gaming'])
            graphics_count = len(self.df[self.df['Usage_Category'] == 'Graphics'])
            if graphics_count > gaming_count:
                suggestions.append("Consider 'Graphics' category for similar performance")

        return suggestions

    def save_model(self, filepath='recommendation_model.pkl'):
        """Save the trained model"""
        if self.is_trained:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'processor': self.processor
            }
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"Model saved to {filepath}")
        else:
            print("Model not trained yet. Train the model first.")

    def load_model(self, filepath='recommendation_model.pkl'):
        """Load a trained model"""
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.processor = model_data['processor']
            self.is_trained = True
            print(f"Model loaded from {filepath}")
        else:
            print(f"Model file {filepath} not found.")

    def get_feature_importance(self):
        """Get feature importance from the trained model"""
        if not self.is_trained:
            print("Model not trained yet.")
            return None

        # Get feature names
        X, _, _ = self.processor.prepare_for_ml()
        feature_names = X.columns.tolist()

        # Get importance scores
        importance_scores = self.model.feature_importances_

        # Create DataFrame
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Importance': importance_scores
        }).sort_values('Importance', ascending=False)

        return importance_df

    def predict_usage_category(self, laptop_specs):
        """Predict usage category for a laptop with given specs"""
        if not self.is_trained:
            print("Model not trained yet.")
            return None

        # This would be used for new laptops not in the dataset
        # Implementation would depend on the specific use case
        pass

if __name__ == "__main__":
    # Test the recommendation engine
    from data_processor import LaptopDataProcessor

    print("Loading and processing data...")
    processor = LaptopDataProcessor()
    processor.load_data()
    processor.clean_data()
    processor.feature_engineering()

    print("Initializing recommendation engine...")
    engine = LaptopRecommendationEngine(processor)
    engine.train_recommendation_model()

    # Test recommendations
    test_preferences = {
        'usage_type': 'Gaming',
        'budget_min': 1000,
        'budget_max': 1500,
        'weight_preference': 'Any'
    }

    print(f"\nTesting recommendations for: {test_preferences}")
    recommendations, message = engine.get_smart_recommendations(test_preferences)

    print(f"\n{message}")
    if not recommendations.empty:
        print("\nTop 3 Recommendations:")
        for i, (_, laptop) in enumerate(recommendations.head(3).iterrows(), 1):
            print(f"\n{i}. {laptop['Laptop']}")
            print(f"   Price: €{laptop['Final Price']:.2f}")
            print(f"   Score: {laptop['Recommendation_Score']:.1f}/100")
            print(f"   Reason: {laptop['Recommendation_Reason']}")

    # Show feature importance
    print("\nFeature Importance:")
    importance = engine.get_feature_importance()
    print(importance.head(10))
